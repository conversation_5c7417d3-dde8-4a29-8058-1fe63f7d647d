import { useState, useEffect, useCallback } from 'react'
import { ConfigManager } from '../lib/storage'

export interface ToolbarState {
  isActive: boolean
  elementCount: number
  isSelectionMode: boolean
  selectedElements: string[]
}

export function useToolbarState() {
  const [state, setState] = useState<ToolbarState>({
    isActive: false,
    elementCount: 0,
    isSelectionMode: false,
    selectedElements: []
  })

  // Load initial state
  useEffect(() => {
    const loadState = async () => {
      try {
        const config = await ConfigManager.getConfig()
        setState(prevState => ({
          ...prevState,
          isActive: config.enabled
        }))
      } catch (error) {
        console.warn('Failed to load toolbar state:', error)
      }
    }

    loadState()
  }, [])

  // Update active state
  const setActive = useCallback(async (active: boolean) => {
    try {
      await ConfigManager.setConfig({ enabled: active })
      
      setState(prevState => ({
        ...prevState,
        isActive: active
      }))
    } catch (error) {
      console.error('Failed to update active state:', error)
    }
  }, [])

  // Update element count
  const setElementCount = useCallback((count: number) => {
    setState(prevState => ({
      ...prevState,
      elementCount: count
    }))
  }, [])

  // Toggle selection mode
  const toggleSelectionMode = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      isSelectionMode: !prevState.isSelectionMode
    }))
  }, [])

  // Add selected element
  const addSelectedElement = useCallback((elementId: string) => {
    setState(prevState => ({
      ...prevState,
      selectedElements: [...prevState.selectedElements, elementId],
      elementCount: prevState.selectedElements.length + 1
    }))
  }, [])

  // Remove selected element
  const removeSelectedElement = useCallback((elementId: string) => {
    setState(prevState => {
      const newElements = prevState.selectedElements.filter(id => id !== elementId)
      return {
        ...prevState,
        selectedElements: newElements,
        elementCount: newElements.length
      }
    })
  }, [])

  // Clear all selected elements
  const clearSelectedElements = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      selectedElements: [],
      elementCount: 0
    }))
  }, [])

  return {
    ...state,
    setActive,
    setElementCount,
    toggleSelectionMode,
    addSelectedElement,
    removeSelectedElement,
    clearSelectedElements
  }
}