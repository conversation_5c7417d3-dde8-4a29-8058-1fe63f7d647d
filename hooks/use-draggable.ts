import { useCallback, useEffect, useRef, useState } from 'react'
import { PanInfo } from 'framer-motion'

export interface SnapAreas {
  topLeft?: boolean
  topRight?: boolean
  bottomLeft?: boolean
  bottomRight?: boolean
  topCenter?: boolean
  bottomCenter?: boolean
}

export interface Position {
  x: number
  y: number
}

export interface UseDraggableOptions {
  ref: React.RefObject<HTMLElement>
  snapAreas?: SnapAreas
  storageKey?: string
  containerSelector?: string
  snapThreshold?: number
}

export interface DragHandlers {
  onDragStart: (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => void
  onDrag: (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => void
  onDragEnd: (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => void
}

export function useDraggable({
  ref,
  snapAreas = { topLeft: true, topRight: true, bottomLeft: true, bottomRight: true },
  storageKey = 'draggable-position',
  containerSelector = 'body',
  snapThreshold = 100
}: UseDraggableOptions) {
  const [position, setPosition] = useState<Position>({ x: 20, y: 20 })
  const [isDragging, setIsDragging] = useState(false)
  const [startPosition, setStartPosition] = useState<Position>({ x: 0, y: 0 })
  const dragVelocity = useRef({ x: 0, y: 0 })

  // Load position from storage on mount
  useEffect(() => {
    if (storageKey && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(storageKey)
        if (stored) {
          const storedPosition = JSON.parse(stored)
          setPosition(storedPosition)
        }
      } catch (error) {
        console.warn('Failed to load draggable position from storage:', error)
      }
    }
  }, [storageKey])

  // Save position to storage
  const savePosition = useCallback((newPosition: Position) => {
    if (storageKey && typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, JSON.stringify(newPosition))
      } catch (error) {
        console.warn('Failed to save draggable position to storage:', error)
      }
    }
  }, [storageKey])

  // Get container bounds
  const getContainerBounds = useCallback(() => {
    const container = document.querySelector(containerSelector) || document.body
    const rect = container.getBoundingClientRect()
    return {
      width: rect.width,
      height: rect.height,
      left: 0,
      top: 0
    }
  }, [containerSelector])

  // Calculate snap position based on velocity and current position
  const calculateSnapPosition = useCallback((currentPos: Position, velocity: { x: number, y: number }) => {
    const containerBounds = getContainerBounds()
    const elementRect = ref.current?.getBoundingClientRect()
    
    if (!elementRect) return currentPos

    const elementWidth = elementRect.width
    const elementHeight = elementRect.height
    
    // Determine snap quadrant based on position and velocity
    const centerX = containerBounds.width / 2
    const centerY = containerBounds.height / 2
    
    const finalX = currentPos.x + velocity.x * 0.1 // Predict future position based on velocity
    const finalY = currentPos.y + velocity.y * 0.1
    
    let snapX = currentPos.x
    let snapY = currentPos.y
    
    // Horizontal snapping
    if (Math.abs(velocity.x) > snapThreshold || finalX < centerX) {
      // Snap to left side
      if (snapAreas.topLeft || snapAreas.bottomLeft) {
        snapX = 20
      }
    } else {
      // Snap to right side
      if (snapAreas.topRight || snapAreas.bottomRight) {
        snapX = containerBounds.width - elementWidth - 20
      }
    }
    
    // Vertical snapping
    if (Math.abs(velocity.y) > snapThreshold || finalY < centerY) {
      // Snap to top
      if (snapAreas.topLeft || snapAreas.topRight) {
        snapY = 20
      }
    } else {
      // Snap to bottom
      if (snapAreas.bottomLeft || snapAreas.bottomRight) {
        snapY = containerBounds.height - elementHeight - 20
      }
    }
    
    // Ensure within bounds
    snapX = Math.max(0, Math.min(snapX, containerBounds.width - elementWidth))
    snapY = Math.max(0, Math.min(snapY, containerBounds.height - elementHeight))
    
    return { x: snapX, y: snapY }
  }, [ref, snapAreas, snapThreshold, getContainerBounds])

  // Drag handlers
  const handleDragStart = useCallback((event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(true)
    setStartPosition({ x: info.point.x, y: info.point.y })
    dragVelocity.current = { x: 0, y: 0 }
  }, [])

  const handleDrag = useCallback((event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const newPosition = {
      x: position.x + info.delta.x,
      y: position.y + info.delta.y
    }
    
    // Update velocity tracking
    dragVelocity.current = {
      x: info.velocity.x,
      y: info.velocity.y
    }
    
    // Clamp to container bounds during drag
    const containerBounds = getContainerBounds()
    const elementRect = ref.current?.getBoundingClientRect()
    
    if (elementRect) {
      newPosition.x = Math.max(0, Math.min(newPosition.x, containerBounds.width - elementRect.width))
      newPosition.y = Math.max(0, Math.min(newPosition.y, containerBounds.height - elementRect.height))
    }
    
    setPosition(newPosition)
  }, [position, ref, getContainerBounds])

  const handleDragEnd = useCallback((event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false)
    
    // Calculate final snap position based on velocity
    const finalPosition = calculateSnapPosition(position, dragVelocity.current)
    
    setPosition(finalPosition)
    savePosition(finalPosition)
  }, [position, calculateSnapPosition, savePosition])

  // Drag handlers object
  const dragHandlers: DragHandlers = {
    onDragStart: handleDragStart,
    onDrag: handleDrag,
    onDragEnd: handleDragEnd
  }

  return {
    position,
    isDragging,
    dragHandlers,
    setPosition: (newPos: Position) => {
      setPosition(newPos)
      savePosition(newPos)
    }
  }
}