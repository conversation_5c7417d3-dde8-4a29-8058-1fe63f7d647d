# Snapmark 浏览器插件开发任务计划

## 项目概述
开发一款浏览器插件，帮助AI Agent精准识别要修改的页面元素。参考Stagewise项目架构，使用Plasmo框架、shadcn/ui和magicui组件库。

## 技术栈
- **框架**: Plasmo (浏览器插件框架)
- **UI组件**: shadcn/ui + magicui
- **语言**: TypeScript + React
- **样式**: Tailwind CSS
- **存储**: Chrome Storage API

## 架构设计
- **Popup**: 插件设置界面（开关、配置选项）
- **Content Script**: 注入页面的可拖动工具栏 + DOM选择器
- **Background**: 消息通信和数据管理

---

## Phase 1: UI架构设计和技术栈配置 🏗️

### Task 1.1: 配置shadcn/ui和magicui组件库
- [ ] 安装和配置shadcn/ui
- [ ] 集成magicui组件
- [ ] 配置Tailwind CSS主题
- [ ] 创建基础组件导出文件

### Task 1.2: 设计popup设置界面结构
- [ ] 插件启用/禁用开关
- [ ] 选择器配置选项
- [ ] AI Agent选择器
- [ ] 设置项存储逻辑设计

### Task 1.3: 设计可拖动toolbar界面结构（仿Stagewise）
- [ ] 工具栏基础布局设计
- [ ] 选中元素列表展示
- [ ] 拖拽控制器设计
- [ ] 工具栏状态管理设计

---

## Phase 2: Popup设置界面实现 ⚙️

### Task 2.1: 实现插件状态管理
- [ ] 创建全局状态管理（插件开启/关闭）
- [ ] 实现Chrome storage状态持久化
- [ ] 添加状态变化监听器

### Task 2.2: 实现设置选项界面
- [ ] 创建现代化的popup UI界面
- [ ] 实现插件开关切换组件
- [ ] 添加选择器配置选项
- [ ] 实现AI Agent选择下拉框
- [ ] 添加设置保存/重置功能

---

## Phase 3: 可拖动Toolbar实现 🎯

### Task 3.1: 创建content script注入的拖动工具栏
- [ ] 创建content script入口文件
- [ ] 实现工具栏DOM结构注入
- [ ] 设置工具栏样式隔离（Shadow DOM或CSS-in-JS）
- [ ] 实现工具栏显示/隐藏控制

### Task 3.2: 实现工具栏拖拽功能和位置记忆
- [ ] 实现拖拽事件处理
- [ ] 添加拖拽边界限制
- [ ] 实现位置记忆存储
- [ ] 添加拖拽视觉反馈

### Task 3.3: 实现选中元素列表展示和管理
- [ ] 创建元素列表展示组件
- [ ] 实现元素项的展示格式
- [ ] 添加删除单个元素功能
- [ ] 实现清空所有元素功能
- [ ] 添加元素详情查看功能

---

## Phase 4: DOM元素选择核心功能 🎨

### Task 4.1: 移植Stagewise的元素选择逻辑
- [ ] 从Stagewise提取核心选择器代码
- [ ] 适配到Content Script环境
- [ ] 实现`getElementAtPoint`函数
- [ ] 添加元素过滤逻辑（排除工具栏等）

### Task 4.2: 实现鼠标悬停高亮和点击选择
- [ ] 实现鼠标移动事件监听
- [ ] 添加元素悬停高亮效果
- [ ] 实现点击选择元素功能
- [ ] 添加选择模式切换逻辑

### Task 4.3: 实现选中元素的视觉反馈
- [ ] 创建高亮覆盖层组件
- [ ] 实现选中元素边框高亮
- [ ] 添加元素标签显示（标签名、类名等）
- [ ] 实现多元素选中状态管理

---

## Phase 5: 元素信息提取和存储 📊

### Task 5.1: 实现XPath生成和元素属性提取
- [ ] 移植XPath生成算法
- [ ] 实现元素属性完整提取
- [ ] 添加元素位置信息获取
- [ ] 实现元素文本内容提取

### Task 5.2: 设计元素数据结构和Chrome storage存储
- [ ] 定义SelectedElement数据接口
- [ ] 实现Chrome storage读写操作
- [ ] 添加数据序列化/反序列化
- [ ] 实现存储容量管理

### Task 5.3: 实现popup和content script之间的消息通信
- [ ] 设计消息通信协议
- [ ] 实现popup到content script通信
- [ ] 实现content script到popup通信
- [ ] 添加消息错误处理

---

## Phase 6: AI Agent通讯接口 🤖

### Task 6.1: 设计标准化的元素上下文消息格式
- [ ] 参考Stagewise消息格式设计
- [ ] 定义统一的上下文数据结构
- [ ] 实现消息格式序列化
- [ ] 添加消息验证机制

### Task 6.2: 实现与外部AI工具的通讯接口
- [ ] 设计插件间通信机制
- [ ] 实现剪贴板数据传输
- [ ] 添加导出功能（JSON/XML格式）
- [ ] 实现与主流AI工具的集成接口

---

## 开发原则 📋

1. **渐进式开发**: 先基础功能，后高级功能
2. **UI先行**: 先设计界面，后实现逻辑
3. **模块化**: 每个Phase独立可测试
4. **用户体验**: 界面流畅，操作直观
5. **性能优化**: 避免影响页面性能
6. **兼容性**: 支持主流浏览器和网站

## 里程碑 🎯

- **Week 1-2**: Phase 1-2 完成基础UI架构
- **Week 3-4**: Phase 3-4 完成核心功能
- **Week 5-6**: Phase 5-6 完成数据处理和AI集成
- **Week 7**: 测试、优化、文档完善

## 预期成果 ✨

1. 现代化的浏览器插件界面
2. 流畅的DOM元素选择体验
3. 完整的元素信息提取能力
4. 与AI工具的无缝集成
5. 高性能和良好兼容性