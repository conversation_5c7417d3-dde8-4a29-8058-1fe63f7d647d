import { useState, useEffect } from "react"
import { Switch } from "~components/ui/switch"
import { Label } from "~components/ui/label"
import { Button } from "~components/ui/button"
import { ConfigManager } from "~lib/storage"
import "./style.css"

function OptionsPage() {
  const [isEnabled, setIsEnabled] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saved, setSaved] = useState(false)

  // Load initial configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await ConfigManager.getConfig()
        setIsEnabled(config.enabled)
        setLoading(false)
      } catch (error) {
        console.error('Failed to load config:', error)
        setLoading(false)
      }
    }
    loadConfig()
  }, [])

  const handleToggle = async (enabled: boolean) => {
    try {
      await ConfigManager.setConfig({ enabled })
      setIsEnabled(enabled)
      setSaved(true)
      
      // Clear saved indicator after 2 seconds
      setTimeout(() => setSaved(false), 2000)
    } catch (error) {
      console.error('Failed to save config:', error)
    }
  }

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto p-8">
        <div className="text-center">Loading...</div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-8 bg-white min-h-screen">
      <div className="space-y-8">
        {/* Header */}
        <div className="border-b pb-6">
          <h1 className="text-3xl font-bold text-gray-900">Snapmark Settings</h1>
          <p className="text-gray-600 mt-2">
            Configure your AI-assisted page element identification tool
          </p>
        </div>

        {/* Main Settings */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="enable-extension" className="text-base font-medium">
                  Enable Extension
                </Label>
                <p className="text-sm text-gray-600">
                  Turn on Snapmark to show the toolbar on web pages
                </p>
              </div>
              <Switch
                id="enable-extension"
                checked={isEnabled}
                onCheckedChange={handleToggle}
              />
            </div>
          </div>

          {/* Status */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                isEnabled ? 'bg-green-500' : 'bg-gray-400'
              }`} />
              <span className="font-medium">
                Status: {isEnabled ? 'Active' : 'Inactive'}
              </span>
              {saved && (
                <span className="text-green-600 text-sm">✓ Saved</span>
              )}
            </div>
            {isEnabled && (
              <p className="text-sm text-blue-700 mt-2">
                The toolbar will appear on web pages when you visit them.
              </p>
            )}
          </div>

          {/* Future Settings Placeholder */}
          <div className="bg-gray-50 p-6 rounded-lg opacity-50">
            <h3 className="font-medium text-gray-900 mb-3">Advanced Settings</h3>
            <div className="space-y-3 text-sm text-gray-600">
              <div>• Toolbar position preferences</div>
              <div>• Element selection modes</div>
              <div>• AI agent communication settings</div>
              <div>• Export/Import configurations</div>
            </div>
            <p className="text-xs text-gray-500 mt-3">
              These features will be available in future updates.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t pt-6 text-center text-sm text-gray-500">
          <p>Snapmark Extension v0.0.1</p>
          <p className="mt-1">AI-assisted page element identification</p>
        </div>
      </div>
    </div>
  )
}

export default OptionsPage