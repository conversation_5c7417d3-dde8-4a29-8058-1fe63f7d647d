import React, { useState, useEffect } from 'react'
import type { PlasmoCSConfig } from 'plasmo'
import { createRoot } from 'react-dom/client'

import SimpleToolbar from './components/toolbar/simple'
import { ConfigManager } from './lib/storage'
import './style.css'

// Plasmo content script configuration
export const config: PlasmoCSConfig = {
  matches: ['https://*/*'],
  all_frames: false,
  run_at: 'document_end'
}

interface ContentScriptProps {
  anchor?: Element
  shadowHost?: Element
}

const SnapmarkContent: React.FC<ContentScriptProps> = () => {
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showToolbar, setShowToolbar] = useState(false)

  // Load configuration and initialize
  useEffect(() => {
    const initializeExtension = async () => {
      try {
        const config = await ConfigManager.getConfig()
        setIsEnabled(config.enabled)
        setShowToolbar(config.enabled) // Show toolbar if enabled
      } catch (error) {
        console.error('Failed to initialize Snapmark extension:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeExtension()
  }, [])

  // Listen for configuration changes
  useEffect(() => {
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes.snapmark_config) {
        const newConfig = changes.snapmark_config.newValue
        if (newConfig) {
          setIsEnabled(newConfig.enabled)
          setShowToolbar(newConfig.enabled) // Show/hide toolbar based on enabled state
        }
      }
    }

    chrome.storage.onChanged.addListener(handleStorageChange)
    
    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange)
    }
  }, [])

  // Toolbar event handlers
  const handleElementSelect = () => {
    console.log('Starting element selection mode...')
    // TODO: Implement element selection logic
  }

  const handleViewElements = () => {
    console.log('Opening elements view...')
    // TODO: Implement elements view
  }

  const handleOpenSettings = () => {
    console.log('Opening settings...')
    // TODO: Open popup settings or inline settings panel
  }

  // Don't render if loading or toolbar should not be shown  
  if (isLoading || !showToolbar) {
    return null
  }

  return (
    <SimpleToolbar
      onElementSelect={handleElementSelect}
      onViewElements={handleViewElements}
      onOpenSettings={handleOpenSettings}
    />
  )
}

export default SnapmarkContent