import type { SnapmarkConfig, SelectedElement } from "./types"
import { DEFAULT_CONFIG } from "./types"

// Chrome Storage Keys
const STORAGE_KEYS = {
  CONFIG: "snapmark_config",
  SELECTED_ELEMENTS: "snapmark_selected_elements"
} as const

// 配置管理
export class ConfigManager {
  // 获取配置
  static async getConfig(): Promise<SnapmarkConfig> {
    try {
      const result = await chrome.storage.sync.get(STORAGE_KEYS.CONFIG)
      return {
        ...DEFAULT_CONFIG,
        ...result[STORAGE_KEYS.CONFIG]
      }
    } catch (error) {
      console.error("获取配置失败:", error)
      return DEFAULT_CONFIG
    }
  }

  // 保存配置
  static async setConfig(config: Partial<SnapmarkConfig>): Promise<void> {
    try {
      const currentConfig = await this.getConfig()
      const newConfig = { ...currentConfig, ...config }
      await chrome.storage.sync.set({
        [STORAGE_KEYS.CONFIG]: newConfig
      })
    } catch (error) {
      console.error("保存配置失败:", error)
      throw error
    }
  }

  // 重置配置
  static async resetConfig(): Promise<void> {
    try {
      await chrome.storage.sync.set({
        [STORAGE_KEYS.CONFIG]: DEFAULT_CONFIG  
      })
    } catch (error) {
      console.error("重置配置失败:", error)
      throw error
    }
  }

  // 监听配置变化
  static onConfigChange(callback: (config: SnapmarkConfig) => void): () => void {
    const listener = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[STORAGE_KEYS.CONFIG]) {
        callback(changes[STORAGE_KEYS.CONFIG].newValue)
      }
    }
    
    chrome.storage.onChanged.addListener(listener)
    
    // 返回清理函数
    return () => {
      chrome.storage.onChanged.removeListener(listener)
    }
  }
}

// 选中元素管理  
export class ElementsManager {
  // 获取选中元素
  static async getSelectedElements(): Promise<SelectedElement[]> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SELECTED_ELEMENTS)
      return result[STORAGE_KEYS.SELECTED_ELEMENTS] || []
    } catch (error) {
      console.error("获取选中元素失败:", error)
      return []
    }
  }

  // 添加选中元素
  static async addSelectedElement(element: SelectedElement): Promise<void> {
    try {
      const elements = await this.getSelectedElements()
      elements.push(element)
      
      // 限制存储数量（最多100个）
      if (elements.length > 100) {
        elements.splice(0, elements.length - 100)
      }
      
      await chrome.storage.local.set({
        [STORAGE_KEYS.SELECTED_ELEMENTS]: elements
      })
    } catch (error) {
      console.error("添加选中元素失败:", error)
      throw error
    }
  }

  // 删除选中元素
  static async removeSelectedElement(elementId: string): Promise<void> {
    try {
      const elements = await this.getSelectedElements()
      const filteredElements = elements.filter(el => el.id !== elementId)
      
      await chrome.storage.local.set({
        [STORAGE_KEYS.SELECTED_ELEMENTS]: filteredElements
      })
    } catch (error) {
      console.error("删除选中元素失败:", error)
      throw error
    }
  }

  // 清空选中元素
  static async clearSelectedElements(): Promise<void> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.SELECTED_ELEMENTS]: []
      })
    } catch (error) {
      console.error("清空选中元素失败:", error)
      throw error
    }
  }

  // 监听元素变化
  static onElementsChange(callback: (elements: SelectedElement[]) => void): () => void {
    const listener = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[STORAGE_KEYS.SELECTED_ELEMENTS]) {
        callback(changes[STORAGE_KEYS.SELECTED_ELEMENTS].newValue || [])
      }
    }
    
    chrome.storage.onChanged.addListener(listener)
    
    // 返回清理函数
    return () => {
      chrome.storage.onChanged.removeListener(listener)
    }
  }
}