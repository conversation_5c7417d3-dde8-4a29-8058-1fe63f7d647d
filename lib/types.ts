// 插件配置类型定义

export interface SnapmarkConfig {
  // 插件基础设置
  enabled: boolean
  
  // 选择器配置
  selector: {
    highlightColor: string
    highlightOpacity: number
    showElementInfo: boolean
    autoScroll: boolean
  }
  
  // AI Agent配置
  aiAgent: {
    type: AIAgentType
    customEndpoint?: string
  }
  
  // 工具栏配置
  toolbar: {
    position: ToolbarPosition
    size: ToolbarSize
    autoHide: boolean
  }
  
  // 数据导出配置
  export: {
    format: ExportFormat
    includeStyles: boolean
    includeAttributes: boolean
  }
}

export enum AIAgentType {
  CURSOR = "cursor",
  GITHUB_COPILOT = "github-copilot", 
  WINDSURF = "windsurf",
  CLINE = "cline",
  ROO_CODE = "roo-code",
  CUSTOM = "custom"
}

export enum ToolbarPosition {
  TOP_LEFT = "top-left",
  TOP_RIGHT = "top-right", 
  BOTTOM_LEFT = "bottom-left",
  BOTTOM_RIGHT = "bottom-right"
}

export enum ToolbarSize {
  SMALL = "small",
  MEDIUM = "medium",
  LARGE = "large"
}

export enum ExportFormat {
  JSON = "json",
  XML = "xml",  
  CLIPBOARD = "clipboard"
}

// 默认配置
export const DEFAULT_CONFIG: SnapmarkConfig = {
  enabled: false,
  selector: {
    highlightColor: "#3b82f6",
    highlightOpacity: 0.3,
    showElementInfo: true,
    autoScroll: false
  },
  aiAgent: {
    type: AIAgentType.CURSOR
  },
  toolbar: {
    position: ToolbarPosition.TOP_RIGHT,
    size: ToolbarSize.MEDIUM,
    autoHide: false
  },
  export: {
    format: ExportFormat.JSON,
    includeStyles: true,
    includeAttributes: true
  }
}

// 选中元素数据结构
export interface SelectedElement {
  id: string
  nodeType: string
  xpath: string
  attributes: Record<string, string>
  textContent: string
  ownProperties: Record<string, unknown>
  boundingClientRect: {
    top: number
    left: number
    width: number
    height: number
  }
  timestamp: number
  url: string
}