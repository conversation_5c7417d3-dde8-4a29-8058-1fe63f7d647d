# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Snapmark** is a Plasmo-based browser extension that helps AI understand UI modification intent. Users can visually select DOM elements on web pages and describe desired changes, creating a bridge between human intent and AI-driven UI modifications.

## Common Development Commands

### Development Server
```bash
pnpm dev
```
Starts development server with hot reload. Load the extension from `build/chrome-mv3-dev/` in your browser.

### Build Commands
```bash
pnpm build          # Production build
pnpm package        # Create extension package for store submission
```

### Extension Development
- Load unpacked extension from `build/chrome-mv3-dev/` during development
- Main entry points: `popup.tsx` (popup interface), `contents/main.tsx` (content script)
- Extension reloads automatically during development

## Architecture Overview

### Framework Stack
- **Plasmo 0.90.5**: Browser extension framework
- **React 18.2.0 + TypeScript**: UI components with type safety
- **Zustand**: State management with browser storage persistence
- **Tailwind CSS + shadcn/ui**: Styled component system with conflict prevention

### Key Architectural Patterns

#### State Management (`lib/store.ts`)
Uses Zustand with Plasmo Storage for persistence. Core state includes:
- `elements`: Selected DOM elements with metadata
- `isSelecting`: Selection mode toggle
- `isSidebarOpen`: UI state management
- `aiConfig`: AI service configuration
- `shortcuts`: Configurable keyboard shortcuts
- `globalDescription`: Overall change description

#### DOM Interaction System
- `hooks/useElementSelector.ts`: Comprehensive element selection with visual feedback
- `lib/dom.ts`: Element path generation, HTML extraction, highlighting utilities
- High z-index isolation (2147483647) to prevent host page conflicts

#### Component Structure
- `Sidebar`: Main 400px panel with element management
- `FloatingButton`: Entry point trigger
- `ElementCard`: Individual element display and editing
- All components use scoped `.snapmark-extension` CSS class

### Element Selection Workflow
1. Toggle selection mode via floating button or hotkey
2. Mouse hover shows element highlights with visual feedback
3. Click captures element data (selector, HTML, position, description)
4. Element stored in Zustand state with persistence
5. Visual cards in sidebar for editing and management

### Keyboard Shortcuts (`hooks/useKeyboard.ts`)
Default shortcuts (configurable):
- `Ctrl+Shift+S`: Toggle sidebar
- `Ctrl+Shift+E`: Start/stop selection
- `Ctrl+Z`: Undo last selection
- `Alt+C`: Clear all elements
- `ESC`: Exit selection mode

### CSS Isolation Strategy
- Extension styles scoped to `.snapmark-extension`
- High z-index values to overlay host content
- CSS resets within extension scope
- Event capturing for reliable interaction

## File Organization

### Core Files
- `contents/main.tsx`: Content script injection point, main UI rendering
- `popup.tsx`: Extension popup (currently minimal)
- `lib/store.ts`: Zustand state management with persistence
- `lib/types.ts`: TypeScript interfaces for element data, AI config, app state

### Components
- `components/layout/`: Sidebar and FloatingButton
- `components/core/`: ElementCard for element management
- `components/ui/`: shadcn/ui components (Button, Card, Badge, Input, Textarea)

### Utilities
- `lib/dom.ts`: DOM manipulation and element utilities
- `hooks/useElementSelector.ts`: Element selection logic
- `hooks/useKeyboard.ts`: Global keyboard shortcut management
- `lib/utils.ts`: General utility functions

### Styling
- `contents/main.css`: Extension-specific styles with conflict prevention
- `style.css`: Global Tailwind styles
- `tailwind.config.js`: Tailwind configuration for extension files

## Development Notes

### Extension Permissions
- Requires `https://*/*` to work on all HTTPS sites
- Uses Plasmo Storage API for persistent state

### Testing Extension Changes
1. Run `pnpm dev`
2. Load unpacked extension from `build/chrome-mv3-dev/`
3. Test on any HTTPS website
4. Check browser console for content script logs
5. Inspect popup by right-clicking extension icon

### AI Integration Architecture
The codebase includes infrastructure for future AI service integration:
- `AIConfig` type with OpenRouter API support
- Cursor IDE integration (port 3001) planned
- Element data structured for AI prompt processing

### Common Patterns
- All DOM interactions use event capturing for reliability
- State changes trigger automatic persistence via Zustand middleware
- Components use inline styles to avoid CSS conflicts with host pages
- Element validation ensures only interactive/modifiable elements are selectable