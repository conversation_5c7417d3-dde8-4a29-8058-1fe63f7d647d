import React, { useState, useCallback, useRef } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Minimize2, Maximize2 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { Button } from '../ui/button'
import { useToolbarState } from '../../hooks/use-toolbar-state'

interface SimpleToolbarProps {
  onElementSelect?: () => void
  onViewElements?: () => void
  onOpenSettings?: () => void
  className?: string
}

export default function SimpleToolbar({
  onElementSelect,
  onViewElements,
  onOpenSettings,
  className
}: SimpleToolbarProps) {
  const [isMinimized, setIsMinimized] = useState(false)
  const [position, setPosition] = useState({ x: 20, y: 20 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const toolbarRef = useRef<HTMLDivElement>(null)

  const { isActive, elementCount } = useToolbarState()

  const handleMinimizeToggle = useCallback(() => {
    setIsMinimized(!isMinimized)
  }, [isMinimized])

  // Simple drag handlers without Framer Motion
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!toolbarRef.current) return
    
    setIsDragging(true)
    const rect = toolbarRef.current.getBoundingClientRect()
    setDragStart({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return
    
    const newX = e.clientX - dragStart.x
    const newY = e.clientY - dragStart.y
    
    // Simple boundary checking
    const maxX = window.innerWidth - 300
    const maxY = window.innerHeight - 200
    
    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    })
  }, [isDragging, dragStart])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Add global mouse event listeners
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // Glass morphism effect classes
  const glassClasses = cn(
    "backdrop-blur-md bg-white/80 border border-white/20",
    "shadow-lg shadow-black/10",
    "before:absolute before:inset-0 before:rounded-lg",
    "before:bg-gradient-to-br before:from-white/20 before:to-transparent",
    "before:border before:border-white/30 before:pointer-events-none"
  )

  if (isMinimized) {
    return (
      <div
        ref={toolbarRef}
        className={cn(
          "fixed z-[9999] w-10 h-10 rounded-full cursor-move select-none",
          glassClasses,
          isDragging && "scale-110 shadow-2xl",
          className
        )}
        style={{ 
          left: `${position.x}px`, 
          top: `${position.y}px`,
          transition: isDragging ? 'none' : 'transform 0.2s ease'
        }}
        onMouseDown={handleMouseDown}
      >
        <Button
          variant="ghost"
          size="sm"
          className="w-full h-full p-0 rounded-full hover:bg-white/20"
          onClick={handleMinimizeToggle}
        >
          <Maximize2 className="w-4 h-4 text-gray-700" />
        </Button>
      </div>
    )
  }

  return (
    <div
      ref={toolbarRef}
      className={cn(
        "fixed z-[9999] rounded-lg cursor-move select-none",
        "min-w-[280px] p-3",
        glassClasses,
        isDragging && "scale-105 shadow-2xl ring-2 ring-blue-500/20",
        className
      )}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transition: isDragging ? 'none' : 'all 0.2s ease'
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Toolbar Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <MousePointer className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-800">
            Snapmark
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="w-6 h-6 p-0 hover:bg-white/20"
          onClick={handleMinimizeToggle}
        >
          <Minimize2 className="w-3 h-3 text-gray-600" />
        </Button>
      </div>

      {/* Status Indicator */}
      <div className="flex items-center gap-2 mb-3 p-2 rounded-md bg-white/30">
        <div className={cn(
          "w-2 h-2 rounded-full",
          isActive ? "bg-green-500" : "bg-gray-400"
        )} />
        <span className="text-xs text-gray-700">
          {isActive ? '✅ Enabled' : '⚪ Disabled'}
        </span>
        {elementCount > 0 && (
          <span className="text-xs text-blue-600 ml-auto">
            {elementCount} elements
          </span>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          variant="default"
          size="sm"
          className="flex-1 text-xs bg-blue-600 hover:bg-blue-700 text-white"
          onClick={onElementSelect}
        >
          <MousePointer className="w-3 h-3 mr-1" />
          Start Selection
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="bg-white/50 hover:bg-white/70 border-white/30"
          onClick={onViewElements}
        >
          <Eye className="w-3 h-3" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="bg-white/50 hover:bg-white/70 border-white/30"
          onClick={onOpenSettings}
        >
          <Settings className="w-3 h-3" />
        </Button>
      </div>

      {/* Drag Handle Indicator */}
      <div className="absolute top-1 left-1/2 transform -translate-x-1/2">
        <div className="w-6 h-1 bg-white/40 rounded-full" />
      </div>
    </div>
  )
}