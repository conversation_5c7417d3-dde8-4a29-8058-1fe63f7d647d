import { motion } from "framer-motion";
import { cn } from "~lib/utils";

interface AnimatedShinyTextProps {
  text: string;
  className?: string;
  shimmerWidth?: number;
}

export function AnimatedShinyText({
  text,
  className,
  shimmerWidth = 100,
}: AnimatedShinyTextProps) {
  return (
    <div
      className={cn(
        "mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70",
        className,
      )}
    >
      <div className="inline-flex items-center justify-center px-4 py-1 transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400">
        <span className="relative inline-block overflow-hidden">
          <span className="relative z-10">{text}</span>
          <motion.span
            className="absolute inset-0 z-0 bg-gradient-to-r from-transparent via-white/90 to-transparent dark:via-white/20"
            style={{ width: `${shimmerWidth}%` }}
            initial={{ x: "-100%" }}
            animate={{ x: "100%" }}
            transition={{
              duration: 2,
              ease: "easeInOut",
              repeat: Infinity,
              repeatDelay: 1,
            }}
          />
        </span>
      </div>
    </div>
  );
}