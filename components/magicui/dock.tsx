import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";
import React from "react";
import { cn } from "~lib/utils";

export interface DockProps {
  children: React.ReactNode;
  className?: string;
}

export interface DockItemProps {
  children: React.ReactNode;
  className?: string;
}

const Dock = React.forwardRef<HTMLDivElement, DockProps>(
  ({ children, className }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn(
          "mx-auto flex h-[58px] w-max gap-2 rounded-2xl border border-neutral-200 bg-white p-2 dark:border-neutral-800 dark:bg-neutral-900",
          className,
        )}
      >
        {children}
      </motion.div>
    );
  },
);

const DockItem = React.forwardRef<HTMLDivElement, DockItemProps>(
  ({ children, className }, ref) => {
    const mouseX = useMotionValue(Infinity);

    return (
      <motion.div
        ref={ref}
        style={{
          scale: useSpring(
            useTransform(mouseX, [-150, 0, 150], [1, 1.2, 1]),
            {
              mass: 0.1,
              stiffness: 150,
              damping: 12,
            },
          ),
        }}
        onMouseMove={(e) => mouseX.set(e.nativeEvent.offsetX - 50)}
        onMouseLeave={() => mouseX.set(Infinity)}
        className={cn(
          "flex aspect-square w-10 cursor-pointer items-center justify-center rounded-full bg-neutral-400/20 transition-colors hover:bg-neutral-400/40",
          className,
        )}
      >
        {children}
      </motion.div>
    );
  },
);

Dock.displayName = "Dock";
DockItem.displayName = "DockItem";

export { Dock, DockItem };