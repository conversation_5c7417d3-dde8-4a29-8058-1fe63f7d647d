{"extensionName": {"message": "Snapmark", "description": "Name of the extension"}, "extensionDescription": {"message": "Help AI Truly Understand Your UI Modification Intent", "description": "Description of the extension"}, "appTitle": {"message": "Snapmark", "description": "Application title"}, "appSubtitle": {"message": "AI-assisted page element identification tool", "description": "Application subtitle"}, "enablePlugin": {"message": "Enable Plugin", "description": "Label for plugin enable switch"}, "startSelection": {"message": "Start Element Selection", "description": "Button text to start selecting elements"}, "viewElements": {"message": "View Selected Elements", "description": "Button text to view selected elements"}, "statusEnabled": {"message": "✅ Enabled", "description": "Status text when plugin is enabled"}, "statusDisabled": {"message": "⚪ Disabled", "description": "Status text when plugin is disabled"}, "status": {"message": "Status", "description": "Status label"}}