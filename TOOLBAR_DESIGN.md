# Snapmark Draggable Toolbar Design

## Overview
The Snapmark draggable toolbar is inspired by Stagewise's sophisticated implementation, providing an intuitive interface for AI-assisted page element selection. The toolbar follows modern UI/UX principles with glass morphism design and smooth animations.

## Architecture

### Core Components
```
components/toolbar/
├── index.tsx           # Main toolbar component
hooks/
├── use-draggable.ts    # Draggable behavior logic  
├── use-toolbar-state.ts # State management
content.tsx             # Content script entry point
```

### Key Features

#### 1. **Glass Morphism Design**
- Backdrop blur with subtle transparency
- Gradient overlays and border effects
- Responsive to dragging states with visual feedback

#### 2. **Advanced Dragging System**
- **Smart Snapping**: Automatic positioning to screen corners
- **Velocity-based Behavior**: Predicts final position based on drag velocity
- **Smooth Animations**: Spring physics for natural movement
- **Boundary Clamping**: Prevents toolbar from moving outside viewport
- **Persistent Position**: Saves position to localStorage

#### 3. **Responsive States**
- **Minimized Mode**: Collapses to 40x40px circular button
- **Expanded Mode**: Full toolbar with all controls
- **Dragging State**: Visual feedback with scale and shadow effects
- **Status Indicators**: Real-time display of extension state and element count

#### 4. **Interaction Design**
- **Drag Handle**: Visual indicator at top of toolbar
- **Action Buttons**: 
  - Primary: Start element selection (blue)
  - Secondary: View selected elements (outline)
  - Settings: Configuration access (outline)
- **Status Display**: Active/inactive state with element counter

## Technical Implementation

### Draggable Hook Features
```typescript
interface UseDraggableOptions {
  snapAreas: SnapAreas           // Configure which corners to snap to
  storageKey: string             // localStorage key for persistence
  containerSelector: string      // Drag container boundary
  snapThreshold: number          // Velocity threshold for snapping
}
```

### Smart Snapping Logic
- Calculates future position based on drag velocity
- Snaps to appropriate corner based on trajectory
- Configurable snap areas (topLeft, topRight, bottomLeft, bottomRight)
- Smooth spring transitions to final position

### State Management
- Chrome Storage integration for configuration sync
- Real-time element count tracking  
- Selection mode state management
- Proper cleanup and memory management

## Usage in Content Script

The toolbar automatically:
1. Loads configuration from Chrome Storage
2. Renders only when extension is enabled
3. Listens for configuration changes
4. Provides callbacks for user interactions

## Design Inspiration

Based on Stagewise's implementation with improvements:
- **Simplified State Management**: Reduced complexity while maintaining functionality
- **Modern Glass Design**: Updated visual style for better integration
- **Better Performance**: Optimized drag calculations and animations
- **Plasmo Integration**: Properly integrated with Plasmo framework patterns

## Next Steps

The toolbar interface structure is now complete. The next phase will implement:
1. Element selection overlay system
2. XPath generation and storage
3. AI agent communication interface
4. Advanced selection modes and filtering

This design provides a solid foundation for the core functionality while maintaining excellent user experience and performance.