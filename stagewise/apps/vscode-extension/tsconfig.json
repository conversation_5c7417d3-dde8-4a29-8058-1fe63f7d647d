{"compilerOptions": {"module": "ES2020", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node"], "baseUrl": "."}, "watchOptions": {"watchFile": "useFsEvents", "watchDirectory": "useFsEvents", "synchronousWatchDirectory": true}, "include": ["src", "scripts"], "exclude": ["node_modules", ".vscode-test", "out"]}