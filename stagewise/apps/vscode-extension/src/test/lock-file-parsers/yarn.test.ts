import { describe, expect, it } from 'vitest';
import { getInstalledDependencies } from '../../utils/lock-file-parsers/yarn';

const lockFileDummy = `
# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/code-frame/-/code-frame-7.16.7.tgz#a93f2ab60a13d335807cf7b2b938501e42566714"
  integrity sha512-TDCfO993+c/d2zA5a23fAZf+a/bzt2bI2/zb8cN3TmM15bMAb4J4bI03JAgL5hnP6GgVYiHlJg3a6Q0v3s0+BA==
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/highlight@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/highlight/-/highlight-7.16.7.tgz#56decf50a80e46633630d6655cd294b4e7b8b42f"
  integrity sha512-EWlybI0zM1jA02a5Hj40LdBnB3z5S8S+0rM5J8/QO4a8400a3ZbsG/k4DEakA9XvJ20o9YtneEV0TzN1jB7aA==
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/helper-validator-identifier@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz#43b4f3b5f0d860183b16955a5b1f9b1e9b2be544"
  integrity sha512-sP4ubQW0J+Mof72vCIddevA89s3wIbfG/I+c2mKnC7iSBY53oT62pAx3f7Jv+R43e1K2j5e2s82KZYWM0eLkw==

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.4.2.tgz#58b871a5b879c8def87498777045c556f0df5603"
  integrity sha512-Mti+9225u+iClgYj42OLCh3YMrwI/3Sg2K9cIdn99Ufk1d533cpbL0rY54w9J5Iiyv9q1aJd3L6A52U7m1uP0w==
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

jest-pnp-resolver@^1.0.1:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz#a3a7b4515a8187884d673f8a4ec692305caaf734"
  integrity sha512-8C2g2goaHfd9N4iB6T5mO+a4e1z6i2N2a2ST6E0Fp1rYnpGte9c3M0dYq3yD5v7angc5JvfD2+bBv2Z+T+QJw==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-4.0.0.tgz#f493b8273a388a10e735cfd58f31086214534f37"
  integrity sha512-GSA/8wC2Abwj/LGEI7AYD9/taRgnbP8G/urJcEG1WnbgpG2bb4ZwYk1Pz37q9aJ21eppauwWCIcJsCsIt4k1xA==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz#359b3424a134a66f0b40113c19e5264b1d6f345c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3L05Gw2zUWFPmeK2+SloLhdeLFaeGUbA/TrTwjyFGeKxqT1NMTph0t2x6GgOInNQ==

react-dom@^17.0.2:
  version "17.0.2"
  resolved "https://registry.yarnpkg.com/react-dom/-/react-dom-17.0.2.tgz#d87723381a38357be84d95f8721c5b86927976e1"
  integrity sha512-s4h96aFFtU2daC27qV4U8d1eI9PuT1d019OOy32lUu8+Lg2fPoh9J/Htkr2Jg2yX/p/PifVs/33kC/9k3zR/CQ==
  dependencies:
    "loose-envify" "^1.4.0"
    "scheduler" "^0.20.2"

react@^17.0.2:
  version "17.0.2"
  resolved "https://registry.yarnpkg.com/react/-/react-17.0.2.tgz#83a21ac9c18d73bdea7a1e35f303f8484ba5f519"
  integrity sha512-gAU54F49TnxBe4s2+bmdDfa0d8p/p/L3+eFVOLI9zH0bca+vLzWEv2Xy+5I9s/1Q1H0mbB0aNPVmpM5gJbS0PA==
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://registry.yarnpkg.com/scheduler/-/scheduler-0.20.2.tgz#66f9bf4e754cb5c81f18579169622d1ba1f129e7"
  integrity sha512-1/IxnApxY11ry2A1zogDq4yI5s7T292/2A05vD3yCAY6x/g/xUqPSRvP5o6R2vPFPx3Gk09p+gB9iF/2Gb30Vg==
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
`;

describe('yarn lock file parser', () => {
  it('should parse yarn lock file correctly', () => {
    const dependencies = getInstalledDependencies(lockFileDummy);
    expect(dependencies).toEqual({
      '@babel/code-frame': '7.16.7',
      '@babel/highlight': '7.16.7',
      '@babel/helper-validator-identifier': '7.16.7',
      chalk: '2.4.2',
      'jest-pnp-resolver': '1.2.3',
      'js-tokens': '4.0.0',
      lodash: '4.17.21',
      react: '17.0.2',
      'react-dom': '17.0.2',
      scheduler: '0.20.2',
    });
  });
});
