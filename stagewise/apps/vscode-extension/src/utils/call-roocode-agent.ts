import * as vscode from 'vscode';
import { injectPromptDiagnosticWithCallback } from './inject-prompt-diagnostic-with-callback';
import type { PromptRequest } from '@stagewise/extension-toolbar-srpc-contract';

const ROOCODE_PROMPT_PREFIX = `\n\n
    Ah sorry, ignore the "Fix any issues" statement and the "Current problems detected" statement.
    Instead, implement the following request:
    `;

const ROOCODE_PROMPT_SUFFIX = `\n
    Ignore the following line of code:
    `;

export async function callRoocodeAgent(request: PromptRequest): Promise<void> {
  const prompt =
    `${request.prompt}` +
    `${request.files ? `\n\n use the following files: ${request.files.join('\n')}` : ''}` +
    `${request.images ? `\n\n use the following images: ${request.images.join('\n')}` : ''}`;
  // model: request.model, not supported yet
  // `${request.model ? `\n\n use the following model: ${request.model}` : ''}` +
  // mode: request.mode, not supported yet
  // `${request.mode ? `\n\n use the following mode: ${request.mode}` : ''}`;

  const promptWithPrefix = `${ROOCODE_PROMPT_PREFIX}\n${prompt}${ROOCODE_PROMPT_SUFFIX}`;
  await injectPromptDiagnosticWithCallback({
    prompt: promptWithPrefix,
    callback: () =>
      vscode.commands.executeCommand('roo-cline.fixCode') as Promise<any>,
  });
}
