import * as vscode from 'vscode';
import { injectPromptDiagnosticWithCallback } from './inject-prompt-diagnostic-with-callback';
import type { PromptRequest } from '@stagewise/extension-toolbar-srpc-contract';

const CURSOR_PROMPT_PREFIX = `\`\`\`
    Ah, sorry, it wasn't an error. The user has submitted a change request. Here is the request, please implement it:
    \`\`\``;

export async function callCursorAgent(request: PromptRequest): Promise<void> {
  const prompt =
    `${request.prompt}` +
    `${request.files ? `\n\n use the following files: ${request.files.join('\n')}` : ''}` +
    `${request.images ? `\n\n use the following images: ${request.images.join('\n')}` : ''}`;
  // model: request.model, not supported yet
  // `${request.model ? `\n\n use the following model: ${request.model}` : ''}` +
  // mode: request.mode, not supported yet
  // `${request.mode ? `\n\n use the following mode: ${request.mode}` : ''}`;

  const promptWithPrefix = `${CURSOR_PROMPT_PREFIX}\n${prompt}`;
  await injectPromptDiagnosticWithCallback({
    prompt: promptWithPrefix,
    callback: () =>
      vscode.commands.executeCommand(
        'composer.fixerrormessage',
      ) as Promise<any>,
  });
}
