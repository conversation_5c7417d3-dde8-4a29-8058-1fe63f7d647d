{"name": "stagewise-vscode-extension", "displayName": "stagewise", "private": true, "description": "Visual vibe coding. Right in your codebase.", "version": "0.9.1", "publisher": "stagewise", "icon": "icon.png", "engines": {"vscode": "^1.85.0"}, "license": "AGPL-3.0-only", "categories": ["AI", "Debuggers", "Machine Learning", "Other"], "activationEvents": ["onStartupFinished", "onUri"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "stagewise.setupToolbar", "title": "Auto-setup the stagewise toolbar (AI-Assisted)", "category": "stagewise"}, {"command": "stagewise.showGettingStarted", "title": "Show Getting Started Guide", "category": "stagewise"}, {"command": "stagewise.authenticate", "title": "Login to stagewise", "category": "stagewise"}, {"command": "stagewise.logout", "title": "<PERSON><PERSON><PERSON> from stagewise", "category": "stagewise"}, {"command": "stagewise.checkAuthStatus", "title": "Check login status", "category": "stagewise"}, {"command": "stagewise.setAgent", "title": "Set the preferred agent you want to use with stagewise", "category": "stagewise"}], "icons": {"stagewise-icon": {"description": "Stagewise icon", "default": {"fontPath": "./stagewise-icons.woff", "fontCharacter": "\\E800"}}}, "configuration": {"title": "stagewise", "type": "object", "properties": {"stagewise.telemetry.enabled": {"type": "boolean", "default": true, "description": "Enable anonymous usage data collection to help improve the product. No personal data is collected.", "tags": ["telemetry", "usesOnlineServices"], "markdownDescription": "Enable anonymous usage data collection to help improve the product. No personal data is collected.\n\nRead more about our [telemetry data collection](https://github.com/stagewise-io/stagewise/blob/main/apps/vscode-extension/TELEMETRY.md)."}}}}, "homepage": "https://stagewise.io", "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git"}, "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "keywords": ["stagewise", "cursor", "extension", "ai", "code", "agent"], "scripts": {"vscode:prepublish": "pnpm run build", "run-server": "node ./out/server.js", "build": "webpack --mode production --config webpack.config.js"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/mocha": "^10.0.10", "@types/node": "22.15.2", "@types/vscode": "^1.85.0", "@types/ws": "^8.18.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.3.2", "@yarnpkg/lockfile": "^1.1.0", "dotenv": "^17.2.0", "ovsx": "^0.10.5", "ts-loader": "^9.5.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "3.1.2", "webpack": "^5.100.0", "webpack-cli": "^6.0.1"}, "dependencies": {"@modelcontextprotocol/sdk": "1.15.1", "@stagewise/agent-runtime-vscode": "workspace:*", "@stagewise/agent-interface": "workspace:*", "@stagewise/agent-client": "workspace:*", "@stagewise/extension-toolbar-srpc-contract": "workspace:*", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/js-yaml": "^4.0.9", "@types/yarnpkg__lockfile": "^1.1.9", "axios": "^1.6.7", "cors": "^2.8.5", "express": "^5.1.0", "js-yaml": "^4.1.0", "posthog-node": "^4.17.1", "ws": "^8.18.3", "zod": "3.25.76"}, "turbo": {"tasks": {"build": {"dependsOn": ["^build"], "outputs": ["out/**"]}}}}