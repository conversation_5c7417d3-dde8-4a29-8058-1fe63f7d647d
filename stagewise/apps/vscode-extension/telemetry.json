{"events": {"extension_activated": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "Tracks when the extension is activated", "properties": {"ide": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The IDE being used (VS Code, etc.)"}}}, "toolbar_connected": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when the toolbar in web app dev mode connects to the extension"}, "opened_web_app_workspace": {"classification": "EndUserPseudonymizedInformation", "purpose": "PerformanceAndHealth", "comment": "Tracks when the user opens a workspace with web apps"}, "toolbar_auto_setup_started": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when the automatic toolbar setup process is initiated"}, "toolbar_setup_completed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when the toolbar setup is completed successfully"}, "toolbar_setup_failed": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "Tracks when the toolbar setup fails", "properties": {"error": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The error message (scrubbed of PII)"}}}, "agent_prompt_triggered": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "Tracks when an agent prompt is triggered by the user"}, "activation_error": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "Tracks when the extension activation fails", "properties": {"error": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The error message (scrubbed of PII)"}}}, "extension_deactivated": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when the extension is deactivated"}, "telemetry_disabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user disables telemetry collection. This helps understand opt-out rates and is tracked once when the setting changes before telemetry is actually disabled."}, "telemetry_enabled": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user re-enables telemetry collection after previously disabling it."}, "getting_started_panel_shown": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when the getting started panel is automatically shown to first-time users"}, "getting_started_panel_manual_show": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user manually opens the getting started panel via command"}, "interacted_with_getting_started_panel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user interacts with the getting started panel"}, "dismissed_getting_started_panel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user dismisses the getting started panel"}, "clicked_setup_toolbar_in_getting_started_panel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user clicks the setup toolbar button in the getting started panel"}, "clicked_open_docs_in_getting_started_panel": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user clicks the open docs button in the getting started panel"}, "post_setup_feedback": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "Tracks user feedback after toolbar setup completion", "properties": {"type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of feedback provided by the user"}, "text": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "The feedback text provided by the user"}, "email": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "The email address provided by the user for follow-up (optional)"}}}, "show_toolbar_update_notification": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a new toolbar version is available"}, "toolbar_update_notification_auto_update": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user clicks the update button in the toolbar update notification"}, "toolbar_update_notification_ignored": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user clicks the ignore button in the toolbar update notification"}, "toolbar_update_notification_dismissed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user dismisses the toolbar update notification"}, "toolbar_auto_update_prompt_sent": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a prompt for auto-updating the toolbar is sent"}, "show_toolbar_integration_notification": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user is shown a recommendation to integrate stagewise into their app"}, "toolbar_integration_notification_ignore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user ignores the toolbar integration notification"}, "toolbar_integration_notification_dismissed": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user dismisses the toolbar integration notification"}, "authenticate_command_triggered": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user initiates the authentication process"}, "logout_command_triggered": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user logs out of their account"}, "check_auth_status_command_triggered": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Tracks when a user checks their authentication status"}, "stagewise_agent_prompt_triggered": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "Tracks when the Stagewise Agent is invoked", "properties": {"agentType": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of agent (always 'stagewise' for this event)"}, "hasUserMessage": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the user included a message with the prompt"}, "messageId": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The ID of the message if present"}, "currentUrl": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The current URL where the agent was triggered (may be null)"}, "selectedElementsCount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of elements selected when the agent was triggered"}, "promptSnippetsCount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The number of prompt snippets included"}}}, "stagewise_agent_tool_call_requested": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "comment": "Tracks when the Stagewise Agent requests a tool execution", "properties": {"agentType": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The type of agent (always 'stagewise' for this event)"}, "toolName": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The name of the tool being requested"}, "isClientSide": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the tool runs on the client side"}, "isBrowserRuntime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Whether the tool runs in the browser runtime"}}}, "stagewise_agent_auth_refresh_failed": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "Tracks when automatic token refresh fails in the Stagewise Agent", "properties": {"reason": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The reason for the auth failure (expired, invalid, or missing)"}, "retryAttempt": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "The number of the retry attempt when the failure occurred"}, "error": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "comment": "The error message (scrubbed of PII)"}}}}}