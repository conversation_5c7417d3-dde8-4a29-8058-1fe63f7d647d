{"name": "stagewise", "module": "src/index.ts", "type": "module", "version": "0.1.2", "description": "Stagewise CLI", "author": "stagewise GmbH", "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git"}, "private": false, "bin": {"stagewise": "dist/index.cjs"}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"build": "dotenv -e ../../.env -- tsx build.ts", "dev": "dotenv -e ../../.env -- tsx src/index.ts", "run": "dotenv -e ../../.env -- rsx dist/index.js", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest watch", "test:ui": "vitest run --ui", "test:coverage": "vitest run --coverage", "reset-data": "tsx reset.ts"}, "devDependencies": {"@inquirer/prompts": "^7.0.0", "@stagewise-plugins/angular": "workspace:*", "@stagewise-plugins/react": "workspace:*", "@stagewise-plugins/vue": "workspace:*", "@stagewise/agent-client": "workspace:*", "@stagewise/agent-runtime-node": "workspace:*", "@stagewise/toolbar": "workspace:*", "@types/express": "^4.17.21", "@vitest/coverage-v8": "^2.0.0", "@vitest/ui": "^2.0.0", "axios": "^1.7.0", "chalk": "^5.3.0", "commander": "^14.0.0", "dotenv-cli": "^9.0.0", "env-paths": "^3.0.0", "esbuild": "^0.25.6", "express": "^4.18.2", "http-proxy-middleware": "^3.0.5", "ignore": "^6.0.2", "license-checker": "^25.0.1", "open": "^10.0.0", "posthog-node": "^4.18.0", "typescript": "^5", "vitest": "^2.0.0", "winston": "^3.17.0", "zod": "^3.24.0"}}