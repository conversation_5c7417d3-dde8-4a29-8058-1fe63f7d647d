---
title: Trademark policy
---

export const metadata = {
  title: "Trademark policy",
  description: "Learn if and how you are allowed to use our name in forks and more",
};

# Trademark policy

"stagewise" is a registered trademark in the European Union. Details are accessible under the filing number 019074615 in the EUTM directory of the EUIPO.

## 1. Preamble: Purpose of the Policy and Balancing Mark Protection with Open Source Principles

tiq UG (haftungsbeschränkt) ("tiq", "We", "Us") is the owner of the EU word mark "stagewise" (hereinafter the "Mark"), which represents the open-source software project "stagewise" developed by Us (hereinafter the "Official Project"). This Mark is an important symbol of the quality, origin, and reputation of the Official Project.

We recognize the immense importance of community contributions, forks, and the freedom for further development within the open-source ecosystem. This Policy aims to establish clear rules for the use of our Mark "stagewise" and its variations by third parties, particularly in connection with forks of the Official Project. The goal is to ensure fair coexistence that protects our Mark while preserving the openness and collaboration inherent in the Project. This Policy is intended to prevent confusion among users regarding the origin of software or services and to ensure that the "stagewise" name is not used in a way that harms the Official Project or tiq.

## 2. Definitions

* **"Mark"**: Refers to the registered EU word mark "stagewise" (Registration Number: 019074615 in EUIPO EUTM directory) as well as any associated logos, word/image marks, or other identifiers used by tiq for the Official Project.
* **"Official Project"**: The version of the software released, maintained, and distributed by tiq under the name "stagewise". The official source is [github.com/stagewise-io/stagewise](https://github.com/stagewise-io/stagewise).
* **"Fork"**: An independent line of development of the Official Project's source code, created by a third party under the terms of the open-source license applicable to the Official Project (currently AGPL-3.0).
* **"Name Variation"**: Any designation for a Fork or an associated product or service that contains the word "stagewise" or a sonically, visually, or conceptually similar variation thereof, whether as a component, suffix, prefix, or in combination with other elements.
* **"You"**: Refers to any individual or entity that creates, distributes, or promotes a Fork, or otherwise wishes to use the "stagewise" Mark or Name Variations.

## 3. Permitted Uses of the "stagewise" Mark and Name Variations

In principle, the use of the "stagewise" Mark as part of the name of a Fork or a related product/service is very limited and subject to strict adherence to the following conditions to avoid confusion.

### 3.1. Nominative / Descriptive Reference

You may use the "stagewise" Mark in a purely descriptive manner to refer to the Official Project or to indicate the compatibility of Your own product or service with the Official Project.
* **Permitted Examples:**
    * "MyNewTool, compatible with stagewise"
    * "PluginX for stagewise"
    * "This tutorial describes the use of stagewise"
* In such cases, it must be clear that it is Your product that is compatible with "stagewise," and not a product by tiq. Use Your own brand name or project name clearly and prominently.

### 3.2. Use of Name Variations in Fork Names (Very Restrictive)

We strongly advise against using "stagewise" as a direct component of Your Fork's name. To avoid confusion, Your Fork should have its own distinct name.
If You nevertheless wish to use a Name Variation containing "stagewise," it must:
1.  **Be Clearly Distinguishable:** The name You choose must be primarily characterized by Your own brand or a strongly differentiating addition. The "stagewise" element must not be the dominant or initial part of the name.
2.  **Not Suggest Official Association:** The name must not create the impression that it is an official product of tiq, a version endorsed by Us, or a "better" version of the Official Project.
3.  **Comply with Section 4 (Prohibited Uses) of this Policy.**
4.  **Always be accompanied by the disclaimer as per Section 3.3.**
* **Potentially Permitted Examples (subject to case-by-case review and potential approval by Us) under strict conditions:**
    * "[YourProjectName]-stagewise-module" (if "[YourProjectName]" is clearly dominant)
    * "stagewise-CommunityFeatureX" (if "CommunityFeatureX" is very specific and the community nature is clear)
* **Generally NOT Permitted Examples for Fork names:** See Section 4.1.

### 3.3. Mandatory Disclaimer for Permitted Name Variations

Any Fork that uses a Name Variation exceptionally permitted under this Policy or refers to the Official Project must include the following or a substantially identical notice in a prominent location (e.g., in the README file, on the project website, in the software's "About" dialog):
*"This is an independent project/fork based on the open-source project 'stagewise'. This project is NOT managed, released, endorsed, sponsored, or approved by tiq UG (haftungsbeschränkt), the original developers of 'stagewise'. The official version of 'stagewise' can be found at [https://stagewise.io](https://stagewise.io)."*
Additionally, the original copyright notices and license texts of the Official Project must be fully and unaltered retained and displayed in the Fork, as typically required by the AGPL-3.0.

### 3.4. Prohibition of Third-Party Trademark Registrations

You are prohibited from applying for or using trademark rights (word marks, figurative marks, etc.), domain names, social media profiles, or company names that contain the "stagewise" Mark or any confusingly similar designation, especially if these are claimed for software, software development, or related goods or services.

## 4. Prohibited Uses of the "stagewise" Mark

The following uses of the "stagewise" Mark or Name Variations are expressly prohibited:

### 4.1. Names Causing Likelihood of Confusion with the Official Project

* The sole use of the name "stagewise" for Your Fork or product.
* The use of additions that suggest an official version, a superior variant, or a direct continuation (e.g., "stagewise Core", "stagewise Official", "stagewise Pro", "stagewise+", "stagewise Ultimate", "stagewise Enterprise", "stagewise Version 2.0", "NextGen stagewise").
* The use of names that differ only minimally from the original (e.g., "Stagewize", "Stage-Wise", "The Stagewise Project").
* Using the "stagewise" Mark in a way that suggests Your Fork is the Official Project, or vice-versa.

### 4.2. Names or Presentations Suggesting Official Affiliation or Endorsement by tiq

* Any use that falsely implies support, sponsorship, certification, partnership, or any other official approval by tiq or the maintainer team of the Official Project.
* The imitation of the visual appearance (trade dress, look and feel) of the official "stagewise" websites, logos, or marketing materials.
* The use of names such as "tiq-stagewise-Fork", "stagewise by tiq Community".

### 4.3. Defamatory, Offensive, or Disparaging Use

The "stagewise" Mark or any permitted Name Variation must not be used in a manner that:
* disparages, defames, insults, or discriminates against the "stagewise" Mark, the Official Project, tiq, its employees, or the community.
* is associated with illegal, pornographic, violence-glorifying, hateful, deceptive, or otherwise offensive, harmful, or ethically questionable content or activities.

### 4.4. Use That Weakens or Dilutes the Mark

Any use that is likely to weaken or dilute the distinctiveness of the "stagewise" Mark as an indicator of origin for the Official Project by tiq.

### 4.5. Commercial Use of Name Variations Without Consent

The use of a Name Variation containing "stagewise" for commercial products or services (i.e., those primarily aimed at profit generation) requires the express prior written consent of tiq. This applies regardless of the license of the Fork (AGPL-3.0). While the AGPL-3.0 may permit commercial use of the software, this Trademark Policy governs the use of the *name*.

## 5. Quality Standards (Indirectly)

While We cannot and do not wish to exercise direct quality control over independent Forks, We expect that any use of the "stagewise" name (to the extent exceptionally permitted under this Policy) does not tarnish the reputation of the Official Project. Forks containing malware, exhibiting severe security vulnerabilities, or otherwise significantly endangering the reputation of the "stagewise" name violate this Policy.

## 6. Review Process for Fork Names (Optional)

We reserve the right, but assume no obligation, to review names of Forks or products that contain "stagewise" or a Name Variation. We strongly encourage You to contact Us (see Section 9) if You are uncertain about the permissibility of a planned name. tiq may revoke permission to use a specific name at any time if it violates this Policy.

## 7. Consequences of Violations

Violations of this Trademark Policy may lead to the infringement of our trademark rights and other legal provisions (e.g., competition law, copyright law). tiq reserves the right to take all appropriate legal action in response to violations. This includes, but is not limited to:
* Requesting cessation of use and change of name.
* Issuing a formal warning (Abmahnung) with a request for a cease and desist declaration subject to a contractual penalty.
* Applying for an injunction.
* Filing a lawsuit for injunctive relief.
* Claiming damages.
* Revoking any permission (whether express, implied, or granted generally under this Policy) to use the "stagewise" name or its variations.

## 8. Contact Person and Questions

If You have questions regarding the use of the "stagewise" Mark, are unsure whether Your intended use is permissible, or wish to obtain special permission, please contact Us at: [<EMAIL>](<EMAIL>)

Thank you for your understanding and cooperation in protecting the "stagewise" Mark and fostering a healthy open-source ecosystem.