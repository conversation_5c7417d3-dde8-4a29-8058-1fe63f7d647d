---
title: Terms of Service
---

export const metadata = {
  title: "Terms of Service"
};

# Terms of Service

**Last Updated: July 21, 2025**

### Preamble

These Terms of Service ("Terms") govern the use of the services offered by tiq UG (haftungsbeschränkt), located at Obernstraße 50, 33602 Bielefeld, Germany, registered with the local court (Amtsgericht) of Bielefeld under HRB 45829 ("we," "us," or "tiq").

By creating an account and using our services, you ("User," "you") agree to these Terms. Deviating terms from the User will not be recognized unless we explicitly agree to their validity in writing.

Our services are directed at both consumers and business entities.

### 1. Subject of the Agreement and Scope of Services

1.1. tiq provides a cloud-based AI agent designed to assist users in creating and editing frontends for web applications (the "Service"). The Service is delivered via an IDE extension or a dedicated application that communicates with tiq's cloud infrastructure.

1.2. To use the Service, the User must create an account and install the provided software.

1.3. We are entitled to further develop, change, or limit the Service and its functionalities at any time, provided that the essential features of the Service are maintained.

1.4. We do not guarantee any specific service availability (uptime). Support is provided on a best-effort basis via <NAME_EMAIL> or through our Discord channel, without guaranteed response times.

### 2. Registration, User Account, and Minimum Age

2.1. Registration is completed by providing an email address, to which a login link ("Magic Link") will be sent. In the future, we may also support login via third-party providers (e.g., Google via OAuth).

2.2. **The User must be at least 18 years of age** or have reached the age of legal capacity to enter into binding contracts in their respective jurisdiction. By registering, you warrant that you meet this requirement.

2.3. The User is solely responsible for the security of their account and the confidentiality of their access credentials. All activities that occur under the User's account are attributed to the User. The User agrees to notify us immediately of any suspected unauthorized use of their account.

### 3. License and Intellectual Property Rights

3.1. **Our Rights:** All rights to the Service itself, including the underlying software, AI models, user interfaces, and all related technology, are the exclusive property of tiq or our licensors. We grant the User a limited, non-exclusive, non-transferable right to use the Service in accordance with these Terms for the duration of the contract.

3.2. **Customer Content:** The User grants us a non-exclusive, worldwide, royalty-free right for the duration of the contract to access, temporarily process, and modify the source code and other files of their project ("Customer Content"), solely to the extent technically necessary to provide the Service (e.g., to generate or modify code). We do not permanently store Customer Content or share it with third parties. The intellectual property of the Customer Content remains entirely with the User.

3.3. **Generated Content:** To the extent legally possible, all rights to the content created for the User by the Service (e.g., source code, "Generated Content") are transferred to the User. The User is free to use the Generated Content for personal and commercial projects.

### 4. Optional Use of Data for Service Improvement (AI Training)

4.1. The provision of the Service does **not** require your consent to the use of your data for our product development.

4.2. We offer the User the **voluntary option (Opt-in)** to help us improve our Service. If the User gives their explicit and separate consent, we are entitled to use anonymized or pseudonymized versions of "Generated Content" and chat histories to train and evaluate our AI models for general purposes ("fine-tuning," "evals").

4.3. This consent can be revoked by the User at any time with future effect in the account settings. A revocation will not negatively impact the use of the Service.

4.4. Without this consent, we will only process technical telemetry data (e.g., token consumption, request timestamps) for billing and operational purposes.

### 5. Fees, Payment, and Credits

5.1. **Free Trial:** New users receive a one-time, complimentary amount of starting credits upon registration. The amount of these credits may vary, and there is no legal entitlement to them.

5.2. **Subscription:** Continued use of the Service requires a paid monthly subscription. Current pricing is available on our website.

5.3. **Credit System:**
    a) The monthly subscription fee provides the User with an equivalent amount of credits ("Monthly Credits").
    b) Unused Monthly Credits expire at the end of the monthly billing cycle and do not roll over.
    c) Users with an active subscription may purchase additional credits ("Top-up Credits").
    d) Top-up Credits are valid for at least one (1) year from the date of purchase.
    e) When using the service, the oldest available credits are always consumed first.

5.4. **Payment Processing:** Payments are processed through our payment service provider, Stripe. Their terms of service and privacy policy apply. The User is responsible for providing current and valid payment information.

5.5. **Cancellation:** The subscription can be canceled at any time, with the cancellation taking effect at the end of the current billing period. Any remaining Monthly Credits can be used until the end of that period. Existing Top-up Credits remain available and usable until their expiration date, even after cancellation.

5.6. **Account Deletion:** If the User deletes their account, all remaining credits (both Monthly and Top-up) are immediately and irrevocably forfeited. No refund will be issued.

5.7. **Refunds:** Refunds are generally not provided once the Service has been used. If a User has purchased a subscription but has not used any credits, they may contact <NAME_EMAIL> to request an early cancellation and a possible refund.

### 6. User Obligations and Prohibited Use

6.1. The User agrees to use the Service only in compliance with applicable laws and these Terms.

6.2. It is strictly prohibited to:
    a) Use the Service for any illegal, harmful, racist, or ethically questionable purposes, or to create software with such content.
    b) Overload, disrupt, or compromise the security of tiq's systems, networks, or API (e.g., through spamming, viruses, DoS attacks).
    c) Reverse engineer, decompile, or otherwise attempt to extract the source code or underlying AI models of the Service or its components.
    d) Create multiple free accounts to circumvent the limitations of the starting credits.

6.3. In case of violations of these obligations, we reserve the right to temporarily suspend or terminate the User's account without notice.

### 7. AI Disclaimers and User Responsibility

7.1. The User acknowledges that the Service is based on artificial intelligence and that the Generated Content may contain errors, inaccuracies, or inappropriate content ("hallucinations").

7.2. The Service is designed as an assistive tool. **The User is solely responsible for carefully reviewing, testing, and validating all Generated Content before any productive use.** The use of Generated Content is at the User's own risk. tiq makes no representation or warranty as to the accuracy, completeness, or fitness for a particular purpose of the Generated Content.

### 8. Warranty Disclaimer and Limitation of Liability

8.1. The Service is provided "as is" and "as available." To the extent permitted by law, we disclaim any warranty regarding the factual accuracy of the AI-generated results. Our obligation is to provide a technically functional service.

8.2. We are unrestrictedly liable for intent and gross negligence, as well as for damages arising from injury to life, body, or health.

8.3. In cases of slight negligence, we shall only be liable for the breach of an essential contractual obligation (a "cardinal duty"), the fulfillment of which is essential for the proper execution of the contract and on whose observance the User may regularly rely. In such cases, liability is limited to the foreseeable damage typical for this type of contract.

8.4. Any further liability of tiq is excluded. These limitations of liability also apply in favor of our legal representatives and agents.

### 9. Data Protection and International Data Transfers

9.1. We are committed to protecting your data. The collection, processing, and use of personal data in connection with the Service are governed by our separate Privacy Policy.

9.2. The User acknowledges that our servers and the servers of some of our sub-processors (e.g., cloud hosters, LLM providers) are located in the United States. **Therefore, the use of the Service necessarily involves the transfer of personal data to the USA.**

9.3. This data transfer is safeguarded by recognized legal mechanisms under the GDPR, in particular the **EU-U.S. Data Privacy Framework (DPF)** for certified providers or by concluding the EU's **Standard Contractual Clauses (SCCs)**. You can find more details in our Privacy Policy.

### 10. Term, Termination, and Changes to the Terms

10.1. The contract is concluded for an indefinite period and can be terminated by the User according to the subscription cancellation rules in Section 5.5.

10.2. We reserve the right to terminate this contract with 30 days' notice. The right to extraordinary termination for cause (e.g., for serious breaches of these Terms) remains unaffected.

10.3. We may amend these Terms at any time. We will notify you of any changes at least 30 days in advance via email or a notice within the Service. If you do not object to the changes within 30 days of receiving the notification, the new Terms will be deemed accepted. We will specifically point out this consequence in the notification.

### 11. Final Provisions

11.1. These Terms shall be governed by the laws of the Federal Republic of Germany, excluding the UN Convention on Contracts for the International Sale of Goods (CISG).

11.2. The place of jurisdiction for all disputes arising from this contract shall be Bielefeld, Germany, provided the User is a merchant, a legal entity under public law, or a special fund under public law. For consumers, the statutory places of jurisdiction apply.

11.3. Should any provision of these Terms be or become invalid, in whole or in part, the validity of the remaining provisions shall not be affected. The invalid provision shall be replaced by the applicable statutory provision.