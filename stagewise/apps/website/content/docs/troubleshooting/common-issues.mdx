---
title: Common issues
---

export const metadata = {
  title: "Common issues",
  description: "If you have a problem with using stagewise, this page most likely references your issue",
};



Below are three of the most frequent issues reported by users, along with their current workarounds, tracking references, and next steps.

If your problem is not listed here, please open an issue on [GitHub](https://github.com/stagewise-io/stagewise/issues).


### Table of Contents

- [Prompt Not Appearing in IDE](#prompt-not-appearing-in-ide)
- [Does Not Work in SSH Remote Session on WSL](#does-not-work-in-ssh-remote-session-on-wsl)


## Prompt Not Appearing in IDE

**Symptom:**
The stagewise toolbar loads, you type and send a prompt, but nothing arrives in your IDE.

**Probable Cause:**
Your prompt was sent to a different running instance (window) of the IDE.

In some cases, the IDE might be in an incompatible state and not able to receive.

**Workarounds:**

* Close or deactivate any duplicate IDE sessions before sending prompts.

* Sounds stupid but: Simply retry.

> ℹ️ If this workaround does not solve your problem, please open a PR including full browser console logs captured when you click **Send**.

---

## Does Not Work in SSH Remote Session on WSL

**Symptom:**
stagewise toolbar fails to initialize or send prompts when connecting via SSH remote session on WSL.

**Resolution:**

* stagewise must run on localhost. Accessing stagewise through SSH thus doesn't work.


