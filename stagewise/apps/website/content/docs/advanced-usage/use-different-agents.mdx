---
title: Use different agents
---

export const metadata = {
  title: "Use different agents",
  description: "Learn how to use stagewise with other coding agents",
};

If you want to use stagewise with an IDE-provided chat agent or some other CLI application, here's how to do so.

> We don't actively support custom integrations with agents other than the stagewise agent. But: We encourage our community to build custom integrations! [Here's a guide on how to do so](/docs/developer-guides/build-custom-agent-integrations).

## How to use other agents with stagewise

In order to make stagewise interoperate with other agents, you have to [start the CLI in bridge mode](/docs/advanced-usage/cli-deep-dive#bridge-mode):

```bash
# If your terminal's working directory is the root directory of the repository of your dev app
stagewise -b

# If you want stagewise to load in another directory than the current working directory
stagewise -b -w ~/repos/my-dev-app
```

In bridge mode, the stagewise agent will be disabled and instead, the toolbar will search for other agents that active on your machine.

## Other supported agents

### Use your code editor's integrated agent

The stagewise code editor extension allows you to connect the toolbar to a wide variety of IDE-integrated coding agents.

You can download the extension from the extension marketplace of your code editor. It's called **"stagewise"**.

IDE chat integrations work like this:

  1. stagewise hosts it's toolbar on top of your dev app
  2. Upon opening the toolbar, you will be prompted to connect to one of the other available agents
  2. You send a message through the toolbar
  3. The toolbar will directly send the prompt to the specifc agent
  
> In most cases, you will be required to switch over to the IDE to send the message to the agent.
> This is due to a limitation in the IDE provided APIs.

### Supported IDEs and IDE Extensions

| Agent           | Supported      |
| --------------- | -------------- |
| Cursor          | ✅             |
| GitHub Copilot  | ✅             |
| Windsurf        | ✅             |
| Cline           | ✅             |
| Roo Code        | ✅             |
| Trae            | ✅             |



## Community-driven agent integrations

The following list contains noteworthy community-driven agent integrations:

  - none - feel free to build one!

*stagewise is not affiliated with any of the community-driven integrations and does not support the given integrations.*