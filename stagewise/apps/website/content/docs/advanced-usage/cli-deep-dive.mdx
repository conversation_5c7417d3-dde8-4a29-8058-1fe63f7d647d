---
title: A deep dive into the CLI
---

export const metadata = {
  title: "A deep dive into the CLI",
  description: "Learn about all features the CLI offers to you",
};

In this section, we want to show you all the features our CLI offers to you and how to use it.

## Typical use cases

This area covers a few simple command examples for the most regular use cases.
If you need further information, you can simply skip this information.

### Start stagewise in the current directory

If you want to use stagewise in your dev app, and you have an open terminal right in the folder of said app, you can simply call:

```bash
# If you're using npm as package manager
npx stagewise

# If you're using pnpm as package manager
pnpm dlx stagewise
```

### Use a different (external) agent

If you want to use stagewise [with another agent than the stagewise agent](/docs/advanced-usage/use-different-agents), you have to start the CLI in bridge mode:

```bash
# If you're using npm as package manager
npx stagewise -b

# If you're using pnpm as package manager
pnpm dlx stagewise -b
```

### Start in another working directory

If the app you're working on is located in a different path, simply pass in a `-w, --workspace` (workspace) argument followed by the path where stagewise should make changes:

```bash
# If you're using npm as package manager
npx stagewise -w ~/projects/my-dev-app  

# If you're using pnpm as package manager
pnpm dlx stagewise -w ~/projects/my-dev-app
```

### Host stagewise on a specific port

By default, stagewise exposes your app and the toolbar on port `3100`, but if you want to override this config, do it with the command line argument `-p, --port`:

```bash
# If you're using npm as package manager
npx stagewise -p 3500 # Set the port you want to use

# If you're using pnpm as package manager
pnpm dlx stagewise -p 3500
```

> You can also configure the port in the `stagewise.json`.


### Configure the port on which your dev app runs on

When opening up a new workspace with stagewise, it will ask you on which port the app's dev mode is hosted on. You can also configure this in the `stagewise.json`, but if you want to quickly override this port, use the `-a, --app-port` argument:

```bash
# If you're using npm as package manager
npx stagewise -a 3002 # Set the port on which your dev app is hosted on

# If you're using pnpm as package manager
pnpm dlx stagewise -a 3002
```

## How the CLI works

This section focuses on what capabilities the CLI has and how it actually works.

### Proxy your web app

The stagewise CLI acts as a development proxy server that sits between your browser and your development application. When you access stagewise (typically on port 3100), it:

1. **Serves the stagewise toolbar** for any request where the browser expects an HTML document (detected via the `sec-fetch-dest: document` header)
2. **Proxies all other requests** (API calls, assets, etc.) to your original development app running on its configured port
3. **Handles WebSocket connections** by proxying them to your app while also managing separate WebSocket connections for the stagewise agent

The proxy preserves all headers, cookies, and request characteristics to ensure your app behaves exactly as it would without stagewise. If your development app is not running, stagewise will display an error page instead of crashing.

### Host the toolbar in your web app

The stagewise toolbar is injected into your web application as a modern ES module-based overlay. Here's how it works:

1. **Dynamic HTML generation**: When you navigate to any page, stagewise generates an HTML document that includes your app content plus the toolbar
2. **ES Module imports**: The toolbar uses modern JavaScript import maps to the toolbar as well as any installed plugins
3. **Plugin system**: The CLI automatically discovers and loads compatible plugins based on your project's dependencies (e.g., React, Vue, Angular plugins)
4. **Configuration injection**: A dynamic configuration file is generated that tells the toolbar about your development setup, available plugins, and agent connectivity

The toolbar appears as a floating interface that doesn't interfere with your app's functionality while providing access to stagewise features.

### Connect to the stagewise agent service

When not running in bridge mode, the CLI automatically initializes and hosts the stagewise agent service:

1. **Authentication**: Uses your stored auth token (from `stagewise auth login` or the first start of the CLI) to authenticate with stagewise services
2. **Agent initialization**: Creates a local agent instance that can read and modify files in your workspace
3. **WebSocket server**: Hosts a WebSocket endpoint at `/stagewise-toolbar-app/server/ws` for real-time communication between the toolbar and agent
4. **File operations**: The agent can perform code analysis, file modifications, and other development tasks within your workspace directory

If you're not authenticated, the CLI will still work but agent features will be unavailable. You can run `stagewise auth login` to enable the full agent functionality.

### Bridge to other (external) agents

Bridge mode (`-b` flag) disables the built-in stagewise agent server and allows you to connect to external agents:

1. **No local agent**: The stagewise agent service is not started, reducing resource usage
2. **External connectivity**: The toolbar can connect to agents running elsewhere (like in VS Code extensions or other development tools)
3. **Configuration restrictions**: Bridge mode is incompatible with auth tokens since you're not using the stagewise agent service
4. **Reduced functionality**: Only basic toolbar and proxy features are available; no built-in code assistance

This mode is useful when you want to use stagewise's toolbar and plugin system with a different AI coding assistant.


## The `stagewise.json` config file

The `stagewise.json` file allows you to configure stagewise behavior for your project. This file should be placed in the root directory of your project (the same directory you run `stagewise` from).

### Configuration options

```json
{
  "port": 3100,
  "appPort": 3000,
  "autoPlugins": true,
  "plugins": [
    "@stagewise/react-plugin",
    {
      "name": "my-custom-plugin",
      "path": "./local-plugin/"
    },
    {
      "name": "external-plugin",
      "url": "https://example.com/plugin.js"
    }
  ]
}
```

#### `port` (optional)
- **Type**: `number`
- **Range**: 1-65535  
- **Description**: The port on which stagewise will host your wrapped application
- **Default**: 3100

#### `appPort` (optional)
- **Type**: `number`
- **Range**: 1-65535
- **Description**: The port on which your development application is running
- **Default**: Prompts user on first run if not specified

#### `autoPlugins` (optional)
- **Type**: `boolean`
- **Description**: Whether to [automatically load stagewise plugins](/docs/advanced-usage/install-plugins) based on detected dependencies in your project
- **Default**: `true`

#### `plugins` (optional)
- **Type**: `Array<string | object>`
- **Description**: List of additional plugins to load. Can be npm package names (strings) or objects with custom configuration

**Plugin object format:**
- `name` (required): Display name for the plugin
- `path` (optional): Local file system path to the plugin  
- `url` (optional): Remote URL to load the plugin from

> **Note**: Each plugin object must have either `path` or `url`, but not both.

### Configuration precedence

stagewise resolves configuration from multiple sources in this order (highest priority first):

1. Command-line arguments
2. `stagewise.json` file
3. Default values


## Command-line argument overview

### `-p`, `--port`

This argument is a numeric argument that defines on what port the combination of your dev app and the stagewise toolbar will be hosted on.

If this is not defined, stagewise will try to load this value from the `stagewise.json`.

The default value for this argument is **`3100`**.

### `-a`, `--app-port`

This argument is a numeric argument that defines on what port the original dev app you want to work with is hosted on. 

If this is not defined, stagewise will try to load this value from the `stagewise.json`.

If no `stagewise.json` exists, the user will be prompted to enter this at the start of the stagewise CLI.

### `-w`, `--workspace`

Use this argument to set a path in which stagewise should operate and make changes.

**This should always be the root directory of the repository in which your app under development resides.**

If this argument is not provided, stagewise will simply use the current working directory of the terminal.

### `-s`, `--silent`

When this flag is provided, stagewise will not request user input or guide you through interactive setup processes. This is useful for automated environments or when you want stagewise to use only pre-configured values.

If any required configuration variables are missing in this mode, stagewise will not ask the user and instead fail to start.

### `-v`, `--verbose`

Enable verbose logging to see detailed debug information about stagewise's operation, including:
- Configuration resolution details
- Plugin loading information  
- Proxy request logging
- WebSocket connection details

### `-t`, `--token`

Provide an authentication token for the stagewise agent directly via the command line instead of using stored credentials from `stagewise auth login`. 

> **Note**: This option is incompatible with bridge mode (`-b`).

### `-b`

Enable bridge mode, which disables the built-in stagewise agent server. In this mode, stagewise only provides the toolbar and proxy functionality, allowing you to connect to external agents or use stagewise with other AI coding assistants.

> **Note**: Bridge mode is incompatible with the `--token` option.

## Authentication commands

### `stagewise auth login`

Initiates the OAuth authentication flow to log in to your stagewise account. Opens a browser window for authentication and stores the resulting tokens securely.

### `stagewise auth logout`

Clears all stored authentication tokens and logs you out of stagewise.

### `stagewise auth status`

Displays your current authentication status, including the email address of the logged-in user.

## Telemetry commands

### `stagewise telemetry status`

Shows your current telemetry configuration and explains the available telemetry levels.

### `stagewise telemetry set <level>`

Set the telemetry level for stagewise CLI. Available levels:

- **`off`**: Disable telemetry completely
- **`anonymous`**: Enable telemetry with pseudonymized machine ID
- **`full`**: Enable telemetry with actual user ID and email

Telemetry helps improve stagewise by tracking usage patterns and error reports without capturing sensitive code or request content.
We only use usage data to understand issues and improve the performance of our service and will never sell telemetry data to third-party providers.

