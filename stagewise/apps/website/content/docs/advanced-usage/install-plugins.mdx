---
title: Install Plugins
---

export const metadata = {
  title: "Install Plugins",
  description: "Learn how to create and use stagewise plugins.",
};

Plugins enhance your experience with stagewise by giving you an easy way to add additional capabilities to your toolbar and more information for the agent.


## Automatically load plugins

By default, stagewise is configured to detect the right set of first-party plugins for you specific app without requiring you to do anything.

This can be changed by setting the `autoPlugins` entry in the workspace's `stagewise.json` file.

When starting the CLI, stagewise will understand the setup and used frameworks in your project to load the right plugins. In addition to this, you can still load additional plugins manually.

## Manually load additional plugins

Plugins are integrated by adding them to the list of plugins in your workspace `stagewise.json` file.

Plugins can be loaded through different methods:

### Load from esm.sh (recommended)

If you add plugin entries to your config simply by adding a string with the name of the plugin, it will automatically be loaded from esm.sh:

```json
{
  plugins: [
    "custom-plugin" // This will be loaded from esm.sh
  ]
}
```

### Load from specific URL

If you want stagewise to load a plugin from a dedicated remote endpoint, create an object and configure name and url:

```json
{
  plugins: [
    {
      name: "custom-plugin",
      url: "http://my-cdn.example.com/my-custom-plugin"
    }
  ]
}
```

### Load from local path

For development purposes, it may be helpful to simply load the plugin from a local path:

```json
{
  plugins: [
    {
      name: "custom-plugin",
      path: "/user/account/documents/my-super-plugin/dist"
    }
  ]
}
```


## Community-created plugins

Feel free to build plugins using the guidelines found in our [Plugin Developer Guide](/docs/developer-guides/build-plugins)
and add them to this page by making a contribution to [our project's repository](https://github.com/stagewise-io/stagewise)!