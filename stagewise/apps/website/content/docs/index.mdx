---
title: Getting started
---

export const metadata = {
  title: "Getting started",
  description: "Learn about stagewise and its features.",
};


Welcome to **stagewise** —  The frontend coding agent for production codebases

- 💬 Tell the agent what you want to change
- 🧠 Click on element(s) to let the agent know where a change should happen
- 💡 Let stagewise do the magic!

> Perfect for devs tired of pasting element information and folder paths into prompts. stagewise uses real-time, browser-powered context.

## Features

- ⚡ Works out of the box
- 🧩 Customize and extend functionality with Plugins
- 📖 Open source
- ⛓️ Compatible with all kinds of frameworks
- 🧠 Use our dedicated frontend agent - or any other compatible through our open agent interface!


## Get Started

### 1. Start your web app in development mode

The first thing you should do is to start your app in regular development mode


### 2. Start stagewise

stagewise can be integrated into your workflow without requiring you to install anything!

Simply open another terminal window in the root of your app under development and enter the following:

```bash
npx stagewise
```

or (if you're using pnpm):

```bash
pnpm dlx stagewise
````

And simply follow the short guide of the CLI app to setup your stagewise account.