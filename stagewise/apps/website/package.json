{"name": "website", "private": true, "scripts": {"build": "next build", "dev": "pnpm dlx stagewise@latest & next dev --turbo --port 3000", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@headlessui/react": "2.2.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-navigation-menu": "^1.2.12", "@stagewise/ui": "workspace:*", "framer-motion": "^12.23.3", "fumadocs-core": "15.6.4", "fumadocs-mdx": "11.6.1", "fumadocs-ui": "15.6.3", "geist": "^1.4.2", "lucide-react": "^0.523.0", "next": "15.3.5", "posthog-js": "^1.257.0", "posthog-node": "^4.17.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "zod": "3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.5", "@types/mdx": "^2.0.13", "@types/node": "22.15.2", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "^5.8.3"}}