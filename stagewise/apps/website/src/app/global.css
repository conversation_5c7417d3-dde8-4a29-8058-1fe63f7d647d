@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';
/* Additional watch source for tailwind */
@source "../../node_modules/@stagewise/ui";


@theme {
  --animate-shine: shine 3s ease-in-out infinite;
  --animate-button-shine: button-shine 2s ease-in-out infinite;
}

:root {
  --foreground: var(--color-zinc-950);
  --background: var(--color-zinc-50);
  --primary: var(--color-zinc-950);
  --primary-foreground: var(--color-zinc-50);
  --secondary: var(--color-zinc-200);
  --accent: var(--color-zinc-950);
  --muted: var(--color-zinc-100);
  --border: var(--color-zinc-500);
  --input: var(--color-zinc-200);
  --card: var(--color-zinc-50);
  --color-fd-background: var(--color-zinc-50);
  --color-fd-foreground: var(--color-zinc-950);
  --color-fd-primary: var(--color-zinc-950);
  /* --color-fd-secondary: #ec4899; */
  /* Add more as needed */
}

:root.dark {
  --foreground: var(--color-zinc-50);
  --background: var(--color-zinc-950);
  --primary: var(--color-zinc-50);
  --primary-foreground: var(--color-zinc-950);
  --secondary: var(--color-zinc-800);
  --accent: var(--color-zinc-50);
  --muted: var(--color-zinc-900);
  --border: var(--color-zinc-700);
  --input: var(--color-zinc-800);
  --card: var(--color-zinc-900);
  --color-fd-background: #09090b;
  --color-fd-foreground: #fff;
  --color-fd-primary: #fff;
  /* ... */
}

@keyframes gradient {
  0% {
    opacity: 0.5;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0.5;
    transform: translateX(100%);
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}


@keyframes button-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}


#nd-nav nav {
  @apply max-w-7xl mx-auto;
}