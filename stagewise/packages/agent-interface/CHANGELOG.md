# @stagewise/agent-interface

## 0.2.3

### Patch Changes

- 29c2d1e: Updating dependencies.

## 0.2.2

### Patch Changes

- 69d29b2: Fix message repition in update-parts (update-parts now contain only the update, as expected)

## 0.2.1

### Patch Changes

- 7a10613: Updated dependencies.

## 0.2.0

### Minor Changes

- 6d3e1c7: Adapt agent-interface implementation to allow updateParts without existing part.

## 0.1.3

### Patch Changes

- b328294: Fixing zod validation intersection issue.

## 0.1.2

### Patch Changes

- 787e8e1: Loosen type validation in selectedElements in agent-interface

## 0.1.1

### Patch Changes

- a83ce44: Feat: Exporting user-message-metadata.

## 0.1.0

### Minor Changes

- 32ef6f5: Add a new and more user-friendly adapter to build a nice function-based interface
