{"name": "@stagewise/agent-interface", "author": "tiq UG (haftungsbeschränkt)", "version": "0.2.3", "license": "AGPL-3.0-only", "type": "module", "private": false, "scripts": {"build": "tsup --config build.config.ts", "clean": "rm -rf dist", "dev": "tsup --config build.config.ts", "prebuild": "pnpm clean", "test": "vitest --run", "example:standalone": "tsx examples/standalone-server.ts", "example:express": "tsx examples/express-integration.ts"}, "publishConfig": {"access": "public"}, "files": ["dist"], "exports": {"./toolbar": {"types": "./dist/toolbar.d.ts", "import": "./dist/toolbar.js", "require": "./dist/toolbar.cjs"}, "./agent": {"types": "./dist/agent.d.ts", "import": "./dist/agent.js", "require": "./dist/agent.cjs"}}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "ws": "^8.18.3"}, "devDependencies": {"@trpc/server": "11.4.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/node": "22.15.2", "@types/ws": "^8.18.1", "esbuild": "^0.25.6", "superjson": "2.2.2", "tsup": "^8.4.0", "tsx": "^4.19.2", "typescript": "^5.8.3", "vitest": "^3.2.4", "zod": "3.25.76"}}