# @stagewise/srpc

## 0.2.2

### Patch Changes

- 7a10613: Updated dependencies.

## 0.2.1

### Patch Changes

- 9e7610d: Replace randomUUID calls with (unsafer, but universally working) Math.random generated IDs

## 0.2.0

### Minor Changes

- c3559c8: Add dedicated "exports" to srpc-client and -server. Expect node-runtime in server code and browser-runtime in clienside code.

### Patch Changes

- 0092794: Update license and copyright notices
- 1575df4: Renaming variables to improve clarity.

## 0.2.0-alpha.0

### Minor Changes

- c3559c8: Add dedicated "exports" to srpc-client and -server. Expect node-runtime in server code and browser-runtime in clienside code.

### Patch Changes

- 1575df4: Renaming variables to improve clarity.
