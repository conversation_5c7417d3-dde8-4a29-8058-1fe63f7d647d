{"name": "@stagewise/srpc", "version": "0.2.2", "license": "AGPL-3.0-only", "type": "module", "private": false, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "clean": "rm -rf dist", "dev": "tsup", "test": "vitest run"}, "publishConfig": {"access": "public"}, "dependencies": {"ws": "^8.18.3", "zod": "3.25.76"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./client": {"types": "./dist/client.d.ts", "import": "./dist/client.js", "require": "./dist/client.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./dist/server.cjs"}}, "devDependencies": {"@types/node": "22.15.2", "@types/ws": "^8.18.1", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "3.1.2"}, "turbo": {"tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}}}}