// tsup.config.ts
import { defineConfig } from 'tsup';
var tsup_config_default = defineConfig({
  entry: ['./src/index.ts', './src/client.ts', './src/server.ts'],
  format: ['cjs', 'esm'],
  dts: true,
  clean: true,
});
export { tsup_config_default as default };
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidHN1cC5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9faW5qZWN0ZWRfZmlsZW5hbWVfXyA9IFwiL1VzZXJzL2dsZW5udG93cy9zdGFnZXdpc2UvcGFja2FnZXMvc3JwYy90c3VwLmNvbmZpZy50c1wiO2NvbnN0IF9faW5qZWN0ZWRfZGlybmFtZV9fID0gXCIvVXNlcnMvZ2xlbm50b3dzL3N0YWdld2lzZS9wYWNrYWdlcy9zcnBjXCI7Y29uc3QgX19pbmplY3RlZF9pbXBvcnRfbWV0YV91cmxfXyA9IFwiZmlsZTovLy9Vc2Vycy9nbGVubnRvd3Mvc3RhZ2V3aXNlL3BhY2thZ2VzL3NycGMvdHN1cC5jb25maWcudHNcIjtpbXBvcnQgeyBkZWZpbmVDb25maWcgfSBmcm9tICd0c3VwJztcblxuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKHtcbiAgZW50cnk6IFsnLi9zcmMvaW5kZXgudHMnLCAnLi9zcmMvY2xpZW50LnRzJywgJy4vc3JjL3NlcnZlci50cyddLFxuICBmb3JtYXQ6IFsnY2pzJywgJ2VzbSddLFxuICBkdHM6IHRydWUsXG4gIGNsZWFuOiB0cnVlLFxufSk7XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQXNRLFNBQVMsb0JBQW9CO0FBRW5TLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLE9BQU8sQ0FBQyxrQkFBa0IsbUJBQW1CLGlCQUFpQjtBQUFBLEVBQzlELFFBQVEsQ0FBQyxPQUFPLEtBQUs7QUFBQSxFQUNyQixLQUFLO0FBQUEsRUFDTCxPQUFPO0FBQ1QsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
