# @stagewise/extension-toolbar-srpc-contract

## 0.2.1

### Patch Changes

- 7a10613: Updated dependencies.
- Updated dependencies [7a10613]
  - @stagewise/srpc@0.2.2

## 0.2.0

### Minor Changes

- e4a0864: Revamp toolbar loading mechanism to use iframes

  - Plugins must now be default exported
  - Plugins must use the @stagewise/plugin-builder package to build their plugin
  - Deployed plugins are now default exports. Make sure to update your project accordingly.

## 0.1.3

### Patch Changes

- 2e121ac: Updated the README to clarify how framework-specific packages are named

## 0.1.2

### Patch Changes

- 02bd300: Adding a window-select-hint when multiple windows are selected

## 0.1.1

### Patch Changes

- Updated dependencies [9e7610d]
  - @stagewise/srpc@0.2.1

## 0.1.0

### Minor Changes

- bde4944: Add images, files, mode and model properties to the srpc contract and agent call dispatches
- 79e11fa: Align versions to match 0.3
- f4b085d: Add session management and connection state
- 79e11fa: Align versions to match 0.3

### Patch Changes

- 0092794: Update license and copyright notices
- 1575df4: Renaming variables to improve clarity.
- 058d70b: Make extension-toolbar-srpc-contract tree-shakeable and restructure toolbar-plugin-architecture.
- Updated dependencies [0092794]
- Updated dependencies [c3559c8]
- Updated dependencies [1575df4]
  - @stagewise/srpc@0.2.0

## 0.1.0-alpha.1

### Minor Changes

- f4b085d: Add session management and connection state

## 0.1.0-alpha.0

### Minor Changes

- bde4944: Add images, files, mode and model properties to the srpc contract and agent call dispatches
- 79e11fa: Align versions to match 0.3
- 79e11fa: Align versions to match 0.3

### Patch Changes

- 1575df4: Renaming variables to improve clarity.
- 058d70b: Make extension-toolbar-srpc-contract tree-shakeable and restructure toolbar-plugin-architecture.
- Updated dependencies [c3559c8]
- Updated dependencies [1575df4]
  - @stagewise/srpc@0.2.0-alpha.0
