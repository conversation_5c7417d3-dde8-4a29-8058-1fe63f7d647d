{"name": "react-example", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo dist node_modules", "dev": "vite --port 3005", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "globals": "^16.3.0", "typescript": "^5.8.3", "vite": "^6.3.5"}}