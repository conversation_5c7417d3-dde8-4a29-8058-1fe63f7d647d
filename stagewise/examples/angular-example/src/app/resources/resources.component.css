.right-side {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.pill-group {
  display: flex;
  flex-direction: column;
  align-items: start;
  flex-wrap: wrap;
  gap: 1.25rem;
}

.pill {
  display: flex;
  align-items: center;
  --pill-accent: var(--bright-blue);
  background: color-mix(in srgb, var(--pill-accent) 5%, transparent);
  color: var(--pill-accent);
  padding-inline: 0.75rem;
  padding-block: 0.375rem;
  border-radius: 2.75rem;
  border: 0;
  transition: background 0.3s ease;
  font-family: var(--inter-font);
  font-size: 0.875rem;
  font-style: normal;
  font-weight: 500;
  line-height: 1.4rem;
  letter-spacing: -0.00875rem;
  text-decoration: none;
}

.pill:hover {
  background: color-mix(in srgb, var(--pill-accent) 15%, transparent);
}

.pill-group .pill:nth-child(6n + 1) {
  --pill-accent: var(--bright-blue);
}
.pill-group .pill:nth-child(6n + 2) {
  --pill-accent: var(--french-violet);
}
.pill-group .pill:nth-child(6n + 3),
.pill-group .pill:nth-child(6n + 4),
.pill-group .pill:nth-child(6n + 5) {
  --pill-accent: var(--hot-red);
}

.pill-group svg {
  margin-inline-start: 0.25rem;
}
