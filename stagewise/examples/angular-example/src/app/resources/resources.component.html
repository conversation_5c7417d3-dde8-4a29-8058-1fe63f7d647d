<div class="right-side">
  <div class="pill-group">
    @for (item of [
      { title: 'Explore the Docs', link: 'https://angular.dev' },
      { title: 'Learn with Tutorials', link: 'https://angular.dev/tutorials' },
      { title: 'CLI Docs', link: 'https://angular.dev/tools/cli' },
      { title: 'Angular Language Service', link: 'https://angular.dev/tools/language-service' },
      { title: 'Angular DevTools', link: 'https://angular.dev/tools/devtools' },
    ]; track item.title) {
      <a
        class="pill"
        [href]="item.link"
        target="_blank"
        rel="noopener"
      >
        <span>{{ item.title }}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="14"
          viewBox="0 -960 960 960"
          width="14"
          fill="currentColor"
        >
          <path
            d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h280v80H200v560h560v-280h80v280q0 33-23.5 56.5T760-120H200Zm188-212-56-56 372-372H560v-80h280v280h-80v-144L388-332Z"
          />
        </svg>
      </a>
    }
  </div>
  <app-footer></app-footer>
</div>
