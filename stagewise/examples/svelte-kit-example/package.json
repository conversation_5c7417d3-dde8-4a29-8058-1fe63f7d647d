{"name": "svelte-kit-example", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo .svelte-kit node_modules", "dev": "vite dev --port 3007", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.5", "@sveltejs/vite-plugin-svelte": "5.1.1", "svelte": "^5.35.5", "svelte-check": "^4.1.7", "typescript": "^5.8.3", "vite": "^6.3.5"}}