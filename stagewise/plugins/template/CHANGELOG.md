# @stagewise/plugin-example

## 0.6.2

### Patch Changes

- Updated dependencies [41d9a9b]
- Updated dependencies [29c2d1e]
  - @stagewise/toolbar@0.6.2

## 0.6.1

### Patch Changes

- Updated dependencies [f581a39]
  - @stagewise/toolbar@0.6.1

## 0.6.0

### Minor Changes

- 20d6671: Refactor the toolbar to new UI and new agent interface
- 20d6671: Update plugin API to new interface

### Patch Changes

- Updated dependencies [2c7432e]
- Updated dependencies [20d6671]
  - @stagewise/toolbar@0.6.0

## 0.5.2

### Patch Changes

- Updated dependencies [17f1c51]
  - @stagewise/toolbar@0.5.2

## 0.5.1

### Patch Changes

- Updated dependencies [7a10613]
  - @stagewise/toolbar@0.5.1

## 0.5.0

### Minor Changes

- e4a0864: Revamp toolbar loading mechanism to use iframes

  - Plugins must now be default exported
  - Plugins must use the @stagewise/plugin-builder package to build their plugin
  - Deployed plugins are now default exports. Make sure to update your project accordingly.

### Patch Changes

- Updated dependencies [e4a0864]
  - @stagewise/toolbar@0.5.0

## 0.4.9

### Patch Changes

- Updated dependencies [9682a4a]
  - @stagewise/toolbar@0.4.9

## 0.4.8

### Patch Changes

- Updated dependencies [02b58d8]
- Updated dependencies [33be114]
  - @stagewise/toolbar@0.4.8

## 0.4.7

### Patch Changes

- 2e121ac: Updated the README to clarify how framework-specific packages are named
- Updated dependencies [263c871]
- Updated dependencies [2e121ac]
  - @stagewise/toolbar@0.4.7

## 0.4.6

### Patch Changes

- Updated dependencies [02bd300]
  - @stagewise/toolbar@0.4.6

## 0.4.5

### Patch Changes

- bef562d: Changed branding slogan
- Updated dependencies [bef562d]
- Updated dependencies [ff3a30e]
- Updated dependencies [6144c99]
- Updated dependencies [9e7610d]
- Updated dependencies [1b47ca5]
  - @stagewise/toolbar@0.4.5

## 0.4.4

### Patch Changes

- Updated dependencies [9b96cb5]
  - @stagewise/toolbar@0.4.4

## 0.4.3

### Patch Changes

- Updated dependencies [2ebfe5e]
  - @stagewise/toolbar@0.4.3

## 0.4.2

### Patch Changes

- Updated dependencies [e2cb10f]
  - @stagewise/toolbar@0.4.2

## 0.4.1

### Patch Changes

- Updated dependencies [8f6f8ec]
- Updated dependencies [f44c5f2]
  - @stagewise/toolbar@0.4.1

## 0.4.0

### Patch Changes

- Updated dependencies [aa11e20]
- Updated dependencies [3ab9b64]
  - @stagewise/toolbar@0.4.0

## 0.3.2

### Patch Changes

- Updated dependencies [3d8613e]
  - @stagewise/toolbar@0.3.1

## 0.3.1

### Patch Changes

- efa687b: Add missing "preact" package to peer dependencies

## 0.3.0

### Minor Changes

- 8de8414: No changes: Aligning package versions
- e4230e0: Updating the create-stagewise-plugin script to match the current plugin-sdk.

### Patch Changes

- 0092794: Update license and copyright notices
- Updated dependencies [da84c16]
- Updated dependencies [bca204b]
- Updated dependencies [0092794]
- Updated dependencies [f4b085d]
- Updated dependencies [3b637e8]
- Updated dependencies [ddc9c9b]
- Updated dependencies [0897284]
- Updated dependencies [e148009]
- Updated dependencies [4abc02e]
- Updated dependencies [16fe652]
- Updated dependencies [ce71a0d]
- Updated dependencies [058d70b]
- Updated dependencies [79e11fa]
- Updated dependencies [92407bd]
- Updated dependencies [a5c1d5b]
- Updated dependencies [319e0e1]
  - @stagewise/toolbar@0.3.0

## 0.1.0-alpha.5

### Patch Changes

- Updated dependencies [f4b085d]
  - @stagewise/toolbar@0.3.0-alpha.6

## 0.1.0-alpha.4

### Patch Changes

- Updated dependencies [92407bd]
  - @stagewise/toolbar@0.3.0-alpha.5

## 0.1.0-alpha.3

### Patch Changes

- Updated dependencies [3b637e8]
- Updated dependencies [a5c1d5b]
- Updated dependencies [319e0e1]
  - @stagewise/toolbar@0.3.0-alpha.4

## 0.1.0-alpha.2

### Minor Changes

- 8de8414: No changes: Aligning package versions

### Patch Changes

- Updated dependencies [da84c16]
  - @stagewise/toolbar@0.3.0-alpha.3

## 0.1.0-alpha.1

### Patch Changes

- Updated dependencies [ddc9c9b]
  - @stagewise/toolbar@0.3.0-alpha.2

## 0.1.0-alpha.0

### Minor Changes

- e4230e0: Updating the create-stagewise-plugin script to match the current plugin-sdk.
