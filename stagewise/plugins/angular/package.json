{"name": "@stagewise-plugins/angular", "version": "0.6.2", "type": "module", "keywords": ["stagewise", "toolbar", "ai", "devtool", "agent", "interaction"], "author": "tiq UG (haftungsbeschränkt)", "homepage": "https://stagewise.io", "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git", "directory": "packages/toolbar"}, "publishConfig": {"access": "public"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "default": "./dist/index.es.js"}}, "main": "dist/index.umd.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "license": "AGPL-3.0-only", "scripts": {"clean": "rm -rf .turbo node_modules", "dev": "NODE_ENV=development tsc -b && vite build --mode development", "build": "NODE_ENV=production tsc -b && vite build"}, "peerDependencies": {"@stagewise/plugin-sdk": "workspace:*"}, "dependencies": {}, "devDependencies": {"@stagewise/plugin-sdk": "workspace:*", "typescript": "~5.8.3", "react": "^19.1.0", "react-dom": "^19.1.0", "@types/react": "^19.1.8", "vite": "7.0.6", "@vitejs/plugin-react-swc": "3.7.0"}, "packageManager": "pnpm@10.10.0"}