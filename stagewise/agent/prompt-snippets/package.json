{"name": "@stagewise/agent-prompt-snippets", "version": "0.0.3", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "echo 'No linting for prompts package'", "type-check": "tsc --noEmit"}, "dependencies": {"@stagewise/agent-runtime-interface": "workspace:*", "@stagewise/agent-types": "workspace:*"}, "devDependencies": {"zod": "3.25.76", "typescript": "^5.0.0", "rimraf": "^5.0.0"}}