{"name": "@stagewise/agent-runtime-interface", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "echo 'No linting for types package'", "type-check": "tsc --noEmit"}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.0.0", "zod": "3.25.76"}}