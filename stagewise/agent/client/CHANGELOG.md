# @stagewise-agent/client-sdk

## 0.0.9

### Patch Changes

- 8b6be17: Adding finishReason logs to LLM calls and increasing max tokens

## 0.0.8

### Patch Changes

- 2374189: Add logging to failed api requests.

## 0.0.7

### Patch Changes

- 0aa12fb: Add agent state description validation.

## 0.0.6

### Patch Changes

- 646b5c8: Allow synthetic tool calls before LLM-usage.
- de6826b: Improve error logging and state messages.

## 0.0.5

### Patch Changes

- 85d33e4: Adding access-token refreshs on agent requests to api.stagewise.io

## 0.0.4

### Patch Changes

- 55069a3: Improving the robustness of the agent-sdk.

## 0.0.3

### Patch Changes

- 2f05c00: Adding dynamic description to agent-client-sdk

## 0.0.2

### Patch Changes

- b7ce742: Adding event handlers to the client sdk.
