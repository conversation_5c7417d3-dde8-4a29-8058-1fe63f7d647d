{"name": "@stagewise/agent-client", "version": "0.0.0", "private": true, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"vscode": "^1.85.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist/**/*.js", "dist/**/*.cjs", "dist/**/*.d.ts"], "scripts": {"build": "npm run clean && npm run build:types && npm run build:bundle", "build:types": "tsc --emitDeclarationOnly", "build:bundle": "node build.js", "dev": "node build.js --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "echo 'No linting for prompts package'", "type-check": "tsc --noEmit"}, "dependencies": {"ai": "^4.3.16", "node-fetch": "^3.3.2", "zod": "3.25.76", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@stagewise/agent-interface": "workspace:*", "@stagewise/api-client": "0.0.2", "@stagewise/agent-prompt-snippets": "workspace:*", "@stagewise/agent-runtime-interface": "workspace:*", "@stagewise/agent-tools": "workspace:*", "@stagewise/agent-types": "workspace:*", "esbuild": "^0.20.2", "rimraf": "^5.0.0", "tsx": "^4.7.1", "typescript": "^5.0.0"}}