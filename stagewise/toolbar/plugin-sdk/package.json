{"name": "@stagewise/plugin-sdk", "description": "Stagewise Plugin SDK", "version": "0.6.2", "private": false, "type": "module", "keywords": ["stagewise", "toolbar", "ai", "devtool", "agent", "interaction"], "author": "tiq UG (haftungsbeschränkt)", "license": "AGPL-3.0-only", "scripts": {"dev": "tsx build.ts", "build": "tsx build.ts"}, "homepage": "https://stagewise.io", "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git", "directory": "toolbar/plugin-sdk"}, "exports": {".": {"types": "./dist/index.d.ts"}}, "types": "dist/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public"}, "devDependencies": {"@stagewise/toolbar": "workspace:*", "tsx": "^4.19.2", "typescript": "~5.8.3"}, "peerDependencies": {"@types/react": "^19.1.8"}, "packageManager": "pnpm@10.10.0", "turbo": {"tasks": {"dev": {"outputs": ["dist/**"]}, "build": {"outputs": ["dist/**"]}}}}