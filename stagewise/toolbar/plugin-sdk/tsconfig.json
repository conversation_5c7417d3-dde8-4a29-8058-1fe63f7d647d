{"compilerOptions": {"types": ["node"], "target": "ES2022", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "strict": true, "declaration": true, "emitDeclarationOnly": false, "outDir": "./dist", "rootDir": "./"}, "include": ["build.ts"], "exclude": ["dist", "node_modules", "tmp"]}