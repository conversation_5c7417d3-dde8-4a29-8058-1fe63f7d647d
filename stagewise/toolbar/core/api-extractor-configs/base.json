{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "bundledPackages": ["@stagewise/extension-toolbar-srpc-contract", "class-variance-authority", "class-variance-authority/*", "zod", "@stagewise/agent-interface", "@stagewise/agent-interface/*"], "apiReport": {"enabled": false}, "docModel": {"enabled": false}, "tsdocMetadata": {"enabled": false}, "dtsRollup": {"enabled": true}, "messages": {"extractorMessageReporting": {"ae-missing-release-tag": {"logLevel": "none"}, "ae-forgotten-export": {"logLevel": "none"}}, "tsdocMessageReporting": {"tsdoc-param-tag-missing-hyphen": {"logLevel": "none"}, "tsdoc-at-sign-in-word": {"logLevel": "none"}, "tsdoc-missing-deprecation-message": {"logLevel": "none"}}}}