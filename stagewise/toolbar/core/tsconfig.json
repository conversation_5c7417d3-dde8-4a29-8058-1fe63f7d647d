{"compilerOptions": {"types": ["node", "vite/client"], "target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "react", "paths": {"@/*": ["./src/*"], "tmp/*": ["./tmp/*"]}}, "basePath": ".", "include": ["src"]}