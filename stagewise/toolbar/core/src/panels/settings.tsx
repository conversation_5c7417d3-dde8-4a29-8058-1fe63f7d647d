import {
  <PERSON>,
  PanelContent,
  PanelFooter,
  PanelHeader,
} from '@/components/ui/panel';
import { AgentSelection } from './shared-content/agent-selection';
import {
  DropdownMenu,
  DropdownMenuButton,
  DropdownMenuContent,
  DropdownMenuLinkItem,
} from '@/components/ui/dropdown-menu';
import { MessageCircleQuestionMarkIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function SettingsPanel() {
  return (
    <Panel>
      <PanelHeader title="Settings" />
      <PanelContent>
        <AgentSelection showConnectedDetails />
      </PanelContent>
      <PanelFooter>
        <DropdownMenu>
          <DropdownMenuButton>
            <Button glassy size="sm" variant="secondary">
              <MessageCircleQuestionMarkIcon className="mr-2 size-4" />
              Need help?
            </Button>
          </DropdownMenuButton>
          <DropdownMenuContent>
            <DropdownMenuLinkItem
              href="https://stagewise.io/docs"
              target="_blank"
            >
              Read the docs
            </DropdownMenuLinkItem>
            <DropdownMenuLinkItem
              href="https://discord.gg/y8gdNb4D"
              target="_blank"
            >
              Join the community
            </DropdownMenuLinkItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PanelFooter>
    </Panel>
  );
}
