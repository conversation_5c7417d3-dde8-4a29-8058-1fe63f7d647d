{"name": "@stagewise/toolbar", "private": true, "version": "0.7.0", "type": "module", "description": "stagewise toolbar SDK for AI Agent interaction.", "keywords": ["stagewise", "toolbar", "ai", "devtool", "agent", "interaction"], "author": "tiq UG (haftungsbeschränkt)", "homepage": "https://stagewise.io", "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git", "directory": "packages/toolbar"}, "types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.umd.js", "main": "./dist/index.umd.js", "module": "./dist/index.es.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}, "./plugin-sdk": {"types": "./dist/plugin-sdk/index.d.ts"}}, "files": ["dist"], "license": "AGPL-3.0-only", "scripts": {"clean": "rm -rf .turbo node_modules", "dev": "tsc -b && tsx build.vite.ts development", "dev:examples": "tsc -b && tsx build.vite.ts development", "build:toolbar": "tsc -b && tsx build.vite.ts production", "build": "tsc -b && tsx build.vite.ts production"}, "devDependencies": {"@headlessui/react": "2.2.2", "@microsoft/api-extractor": "^7.52.8", "@stagewise/agent-interface": "workspace:*", "@stagewise/extension-toolbar-srpc-contract": "workspace:*", "@stagewise/srpc": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@trpc/client": "11.4.3", "@types/node": "22.15.2", "@types/react": "^19.1.8", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "bowser": "^2.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "postcss": "^8.5.3", "postcss-prefix-selector": "^2.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "rollup": "^4.44.2", "rollup-plugin-dts": "^6.2.1", "serialize-javascript": "^6.0.2", "tailwind-merge": "^3.2.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.5", "tsup": "^8.4.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.5.4", "zod": "3.25.76"}}